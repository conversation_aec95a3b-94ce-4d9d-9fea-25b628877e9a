#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
应用程序入口。
"""

import os
import sys
import tkinter as tk

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.ui.main_window import MainWindow
from src.utils.logger import get_logger
from src.core.dependency_injection import resolve, configure_core_services
from src.utils.event_system import EventSystem
from src.utils.async_manager import AsyncManager
from src.utils.config_loader import ConfigLoader
from src.data.db_manager import MongoDBManager
from src.core.rule_engine import RuleEngine
from src.core.file_scanner import FileScanner
from src.video_analyzer import VideoAnalyzer
from src.core.file_operations import FileOperations
from src.ui.factory import StandardUIFactory, IUIFactory

logger = get_logger(__name__)

def main():
    """程序入口函数"""
    try:
        # 确保日志目录存在
        os.makedirs('logs', exist_ok=True)
        
        # 配置核心服务
        configure_core_services()
        
        # 创建Tkinter根窗口
        root = tk.Tk()
        
        # 解析所需的依赖
        ui_factory = resolve(IUIFactory)
        ui_factory.set_root_window(root)
        event_system = resolve(EventSystem)
        async_manager = resolve(AsyncManager)
        config_loader = resolve(ConfigLoader)
        db_manager = resolve(MongoDBManager)
        rule_engine = resolve(RuleEngine)
        file_scanner = resolve(FileScanner)
        video_analyzer = resolve(VideoAnalyzer)
        file_operations = resolve(FileOperations)
        
        # 创建主窗口
        main_window = MainWindow(
            root,
            ui_factory,
            event_system,
            async_manager,
            logger,
            config_loader,
            db_manager,
            rule_engine,
            file_scanner,
            video_analyzer,
            file_operations
        )

        # 延迟运行UI诊断
        def run_ui_diagnosis():
            try:
                logger.info("运行UI诊断...")
                issues = main_window.diagnose_ui_issues()
                if issues:
                    logger.warning(f"发现 {len(issues)} 个UI问题")
                else:
                    logger.info("UI诊断通过")
            except Exception as e:
                logger.error(f"UI诊断失败: {e}")

        # 1秒后运行诊断
        root.after(1000, run_ui_diagnosis)

        # 启动应用
        root.mainloop()
        
    except Exception as e:
        logger.error(f"程序启动失败: {e}", exc_info=True)
        sys.exit(1)

if __name__ == '__main__':
    main()