#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库清理工具命令行接口

提供命令行方式使用数据库清理工具的功能。

作者: AI助手
日期: 2024-01-01
版本: 1.0.0
"""

import asyncio
import argparse
import sys
import json
from pathlib import Path
from typing import Dict, Any, List

from .core.config import CleanerConfig, DatabaseConfig, CleanupRuleConfig
from .core.factory import CleanerFactory
from .utils.logger import setup_logging
from .utils.exceptions import CleanerError


class DatabaseCleanerCLI:
    """数据库清理工具命令行接口"""
    
    def __init__(self):
        self.parser = self._create_parser()
        self.logger = None
    
    def _create_parser(self) -> argparse.ArgumentParser:
        """创建命令行参数解析器"""
        parser = argparse.ArgumentParser(
            description="数据库清理工具 - 支持多种数据库的清理、备份和恢复操作",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
使用示例:
  # 使用配置文件清理
  db-cleaner clean --config config.yaml
  
  # 快速清理MongoDB
  db-cleaner clean --type mongodb --host localhost --database test --collections files,folders
  
  # 分析数据库结构
  db-cleaner analyze --type mongodb --host localhost --database test
  
  # 备份指定集合
  db-cleaner backup --type mongodb --host localhost --database test --collections files --output ./backup
  
  # 生成配置文件模板
  db-cleaner generate-config --type mongodb --output config.yaml
            """
        )
        
        # 全局选项
        parser.add_argument('--version', action='version', version='%(prog)s 1.0.0')
        parser.add_argument('--config', '-c', help='配置文件路径')
        parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], 
                          default='INFO', help='日志级别')
        parser.add_argument('--log-file', help='日志文件路径')
        parser.add_argument('--dry-run', action='store_true', help='试运行模式，不执行实际操作')
        
        # 子命令
        subparsers = parser.add_subparsers(dest='command', help='可用命令')
        
        # clean 命令
        clean_parser = subparsers.add_parser('clean', help='清理数据库')
        self._add_database_args(clean_parser)
        clean_parser.add_argument('--collections', help='要清理的集合/表，用逗号分隔')
        clean_parser.add_argument('--conditions', help='清理条件（JSON格式）')
        clean_parser.add_argument('--backup', action='store_true', help='清理前备份')
        clean_parser.add_argument('--confirm', action='store_true', help='自动确认清理操作')
        
        # analyze 命令
        analyze_parser = subparsers.add_parser('analyze', help='分析数据库结构')
        self._add_database_args(analyze_parser)
        analyze_parser.add_argument('--output', help='分析结果输出文件')
        
        # backup 命令
        backup_parser = subparsers.add_parser('backup', help='备份数据')
        self._add_database_args(backup_parser)
        backup_parser.add_argument('--collections', help='要备份的集合/表，用逗号分隔')
        backup_parser.add_argument('--output', required=True, help='备份输出目录')
        backup_parser.add_argument('--format', choices=['json', 'csv'], default='json', help='备份格式')
        
        # list 命令
        list_parser = subparsers.add_parser('list', help='列出数据库集合/表')
        self._add_database_args(list_parser)
        
        # generate-config 命令
        config_parser = subparsers.add_parser('generate-config', help='生成配置文件模板')
        config_parser.add_argument('--type', required=True, choices=['mongodb', 'mysql', 'postgresql'], 
                                 help='数据库类型')
        config_parser.add_argument('--output', required=True, help='配置文件输出路径')
        
        # info 命令
        info_parser = subparsers.add_parser('info', help='显示工具信息')
        
        return parser
    
    def _add_database_args(self, parser: argparse.ArgumentParser):
        """添加数据库连接参数"""
        parser.add_argument('--type', choices=['mongodb', 'mysql', 'postgresql'], 
                          help='数据库类型')
        parser.add_argument('--host', default='localhost', help='数据库主机')
        parser.add_argument('--port', type=int, help='数据库端口')
        parser.add_argument('--username', help='用户名')
        parser.add_argument('--password', help='密码')
        parser.add_argument('--database', help='数据库名称')
        parser.add_argument('--uri', help='数据库连接URI')
    
    async def run(self, args: List[str] = None) -> int:
        """运行命令行工具"""
        try:
            # 解析参数
            parsed_args = self.parser.parse_args(args)
            
            # 设置日志
            self._setup_logging(parsed_args)
            
            # 执行命令
            if parsed_args.command == 'clean':
                return await self._handle_clean(parsed_args)
            elif parsed_args.command == 'analyze':
                return await self._handle_analyze(parsed_args)
            elif parsed_args.command == 'backup':
                return await self._handle_backup(parsed_args)
            elif parsed_args.command == 'list':
                return await self._handle_list(parsed_args)
            elif parsed_args.command == 'generate-config':
                return await self._handle_generate_config(parsed_args)
            elif parsed_args.command == 'info':
                return await self._handle_info(parsed_args)
            else:
                self.parser.print_help()
                return 1
                
        except CleanerError as e:
            print(f"错误: {e}", file=sys.stderr)
            return 1
        except KeyboardInterrupt:
            print("\n操作被用户中断", file=sys.stderr)
            return 1
        except Exception as e:
            print(f"未知错误: {e}", file=sys.stderr)
            return 1
    
    def _setup_logging(self, args):
        """设置日志"""
        log_config = {
            'level': args.log_level,
            'file': args.log_file,
            'color': True
        }
        self.logger = setup_logging(log_config)
    
    def _create_config_from_args(self, args) -> CleanerConfig:
        """从命令行参数创建配置"""
        if args.config:
            # 从配置文件加载
            return CleanerConfig.from_file(args.config)
        else:
            # 从命令行参数创建
            if not args.type:
                raise CleanerError("必须指定数据库类型或配置文件")
            
            # 数据库配置
            db_config = DatabaseConfig(
                type=args.type,
                host=args.host,
                port=args.port,
                username=args.username,
                password=args.password,
                database=args.database,
                uri=args.uri
            )
            
            # 创建配置
            config = CleanerConfig(database=db_config)
            
            # 设置安全选项
            config.safety['dry_run'] = args.dry_run
            
            return config
    
    async def _handle_clean(self, args) -> int:
        """处理清理命令"""
        config = self._create_config_from_args(args)
        
        # 创建清理器
        cleaner = CleanerFactory.create_cleaner(config)
        
        try:
            async with cleaner:
                if args.collections:
                    # 选择性清理
                    collections = [c.strip() for c in args.collections.split(',')]
                    conditions = json.loads(args.conditions) if args.conditions else None
                    
                    report = await cleaner.clean_selective(
                        collections, 
                        conditions, 
                        confirm=args.confirm
                    )
                else:
                    # 清理所有配置的目标
                    report = await cleaner.clean_all(confirm=args.confirm)
                
                # 输出报告
                print(report.get_summary())
                
                return 0 if report.success else 1
                
        except Exception as e:
            self.logger.error(f"清理操作失败: {e}")
            return 1
    
    async def _handle_analyze(self, args) -> int:
        """处理分析命令"""
        config = self._create_config_from_args(args)
        cleaner = CleanerFactory.create_cleaner(config)
        
        try:
            async with cleaner:
                # 发现数据库结构
                schema = await cleaner.discover_database()
                
                # 输出结果
                if args.output:
                    with open(args.output, 'w', encoding='utf-8') as f:
                        json.dump(schema, f, indent=2, ensure_ascii=False, default=str)
                    print(f"分析结果已保存到: {args.output}")
                else:
                    print(json.dumps(schema, indent=2, ensure_ascii=False, default=str))
                
                return 0
                
        except Exception as e:
            self.logger.error(f"分析操作失败: {e}")
            return 1
    
    async def _handle_backup(self, args) -> int:
        """处理备份命令"""
        config = self._create_config_from_args(args)
        cleaner = CleanerFactory.create_cleaner(config)
        
        try:
            async with cleaner:
                if args.collections:
                    collections = [c.strip() for c in args.collections.split(',')]
                else:
                    # 备份所有集合
                    collections = await cleaner.adapter.list_collections()
                
                # 执行备份
                backup_files = await cleaner.backup_targets(collections, args.output)
                
                print(f"备份完成，共创建 {len(backup_files)} 个备份文件:")
                for backup_file in backup_files:
                    print(f"  - {backup_file}")
                
                return 0
                
        except Exception as e:
            self.logger.error(f"备份操作失败: {e}")
            return 1
    
    async def _handle_list(self, args) -> int:
        """处理列表命令"""
        config = self._create_config_from_args(args)
        cleaner = CleanerFactory.create_cleaner(config)
        
        try:
            async with cleaner:
                collections = await cleaner.adapter.list_collections()
                
                print(f"数据库 {config.database.database} 中的集合/表:")
                for collection in collections:
                    print(f"  - {collection}")
                
                return 0
                
        except Exception as e:
            self.logger.error(f"列表操作失败: {e}")
            return 1
    
    async def _handle_generate_config(self, args) -> int:
        """处理生成配置命令"""
        try:
            # 创建配置模板
            template = self._create_config_template(args.type)
            
            # 保存到文件
            output_path = Path(args.output)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            template.save_to_file(output_path)
            
            print(f"配置文件模板已生成: {output_path}")
            return 0
            
        except Exception as e:
            self.logger.error(f"生成配置文件失败: {e}")
            return 1
    
    async def _handle_info(self, args) -> int:
        """处理信息命令"""
        print("数据库清理工具 v1.0.0")
        print("支持的数据库类型:")
        
        databases = CleanerFactory.list_supported_databases()
        for db in databases:
            print(f"  - {db['type']}: {db['description']}")
        
        return 0
    
    def _create_config_template(self, db_type: str) -> CleanerConfig:
        """创建配置模板"""
        # 数据库配置模板
        if db_type == 'mongodb':
            db_config = DatabaseConfig(
                type="mongodb",
                host="localhost",
                port=27017,
                database="your_database"
            )
        elif db_type == 'mysql':
            db_config = DatabaseConfig(
                type="mysql",
                host="localhost",
                port=3306,
                username="root",
                password="password",
                database="your_database"
            )
        elif db_type == 'postgresql':
            db_config = DatabaseConfig(
                type="postgresql",
                host="localhost",
                port=5432,
                username="postgres",
                password="password",
                database="your_database"
            )
        else:
            raise CleanerError(f"不支持的数据库类型: {db_type}")
        
        # 清理规则模板
        rules = [
            CleanupRuleConfig(
                target="collection_or_table_name",
                conditions={"field": "value"},
                backup=True,
                operation="delete",
                batch_size=1000
            )
        ]
        
        return CleanerConfig(
            database=db_config,
            rules=rules
        )


def main():
    """主入口函数"""
    cli = DatabaseCleanerCLI()
    return asyncio.run(cli.run())


if __name__ == '__main__':
    sys.exit(main())
