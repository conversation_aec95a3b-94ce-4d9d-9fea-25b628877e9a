#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库清理器工厂

负责根据配置创建相应的数据库适配器和清理器实例。

作者: AI助手
日期: 2024-01-01
版本: 1.0.0
"""

from typing import Dict, Type, Any, List

from .config import CleanerConfig
from .cleaner import DatabaseCleaner
from ..adapters.base import DatabaseAdapter
from ..adapters.mongodb import MongoDBAdapter
from ..adapters.mysql import MySQLAdapter
from ..adapters.postgresql import PostgreSQLAdapter
from ..utils.exceptions import ConfigurationError


class CleanerFactory:
    """数据库清理器工厂类"""
    
    # 注册的数据库适配器
    _adapters: Dict[str, Type[DatabaseAdapter]] = {
        'mongodb': MongoDBAdapter,
        'mongo': MongoDBAdapter,
        'mysql': MySQLAdapter,
        'postgresql': PostgreSQLAdapter,
        'postgres': PostgreSQLAdapter,
        'pg': PostgreSQLAdapter
    }
    
    @classmethod
    def register_adapter(cls, database_type: str, adapter_class: Type[DatabaseAdapter]):
        """
        注册新的数据库适配器
        
        参数:
            database_type: 数据库类型标识
            adapter_class: 适配器类
        """
        cls._adapters[database_type.lower()] = adapter_class
    
    @classmethod
    def get_available_adapters(cls) -> Dict[str, Type[DatabaseAdapter]]:
        """
        获取所有可用的数据库适配器
        
        返回:
            适配器字典
        """
        return cls._adapters.copy()
    
    @classmethod
    def create_adapter(cls, database_type: str, connection_params: Dict[str, Any]) -> DatabaseAdapter:
        """
        创建数据库适配器
        
        参数:
            database_type: 数据库类型
            connection_params: 连接参数
        
        返回:
            数据库适配器实例
        """
        database_type = database_type.lower()
        
        if database_type not in cls._adapters:
            available_types = list(cls._adapters.keys())
            raise ConfigurationError(
                f"不支持的数据库类型: {database_type}，支持的类型: {available_types}"
            )
        
        adapter_class = cls._adapters[database_type]
        
        try:
            return adapter_class(connection_params)
        except Exception as e:
            raise ConfigurationError(f"创建数据库适配器失败: {e}")
    
    @classmethod
    def create_cleaner(cls, config: CleanerConfig) -> DatabaseCleaner:
        """
        创建数据库清理器
        
        参数:
            config: 清理器配置
        
        返回:
            数据库清理器实例
        """
        if not config.database:
            raise ConfigurationError("数据库配置不能为空")
        
        # 创建数据库适配器
        adapter = cls.create_adapter(
            config.database.type,
            config.database.get_connection_params()
        )
        
        # 创建清理器
        return DatabaseCleaner(adapter, config)
    
    @classmethod
    def create_cleaner_from_dict(cls, config_dict: Dict[str, Any]) -> DatabaseCleaner:
        """
        从字典配置创建清理器
        
        参数:
            config_dict: 配置字典
        
        返回:
            数据库清理器实例
        """
        config = CleanerConfig.from_dict(config_dict)
        return cls.create_cleaner(config)
    
    @classmethod
    def create_cleaner_from_file(cls, config_file: str) -> DatabaseCleaner:
        """
        从配置文件创建清理器
        
        参数:
            config_file: 配置文件路径
        
        返回:
            数据库清理器实例
        """
        config = CleanerConfig.from_file(config_file)
        return cls.create_cleaner(config)
    
    @classmethod
    def validate_config(cls, config: CleanerConfig) -> bool:
        """
        验证配置的有效性
        
        参数:
            config: 清理器配置
        
        返回:
            配置是否有效
        """
        try:
            # 验证数据库配置
            if not config.database:
                raise ConfigurationError("数据库配置不能为空")
            
            # 验证数据库类型
            database_type = config.database.type.lower()
            if database_type not in cls._adapters:
                available_types = list(cls._adapters.keys())
                raise ConfigurationError(
                    f"不支持的数据库类型: {database_type}，支持的类型: {available_types}"
                )
            
            # 验证连接参数
            connection_params = config.database.get_connection_params()
            if not connection_params:
                raise ConfigurationError("数据库连接参数不能为空")
            
            # 验证清理规则
            if config.rules:
                for rule in config.rules:
                    if not rule.target:
                        raise ConfigurationError("清理规则的目标不能为空")
            
            return True
            
        except ConfigurationError:
            raise
        except Exception as e:
            raise ConfigurationError(f"配置验证失败: {e}")
    
    @classmethod
    def get_adapter_info(cls, database_type: str) -> Dict[str, Any]:
        """
        获取适配器信息
        
        参数:
            database_type: 数据库类型
        
        返回:
            适配器信息
        """
        database_type = database_type.lower()
        
        if database_type not in cls._adapters:
            return {"error": f"不支持的数据库类型: {database_type}"}
        
        adapter_class = cls._adapters[database_type]
        
        return {
            "database_type": database_type,
            "adapter_class": adapter_class.__name__,
            "module": adapter_class.__module__,
            "doc": adapter_class.__doc__
        }
    
    @classmethod
    def list_supported_databases(cls) -> List[Dict[str, Any]]:
        """
        列出所有支持的数据库类型
        
        返回:
            支持的数据库类型列表
        """
        databases = []
        
        # 去重，因为可能有别名
        seen_classes = set()
        
        for db_type, adapter_class in cls._adapters.items():
            if adapter_class not in seen_classes:
                seen_classes.add(adapter_class)
                
                databases.append({
                    "type": db_type,
                    "adapter_class": adapter_class.__name__,
                    "description": adapter_class.__doc__ or f"{db_type.upper()} 数据库适配器"
                })
        
        return databases
