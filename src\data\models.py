#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据模型定义

该模块定义了智能文件管理器使用的数据模型:
- FileInfo: 文件信息模型
- VideoInfo: 视频信息模型
- ScanResult: 扫描结果模型

作者: AI助手
日期: 2023-06-01
版本: 1.0.0
"""

from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional, Tuple, Union
from datetime import datetime
from pathlib import Path
from src.utils.format_utils import _normalize_path


@dataclass
class FileInfo:
    """
    文件信息模型
    
    存储文件的详细信息
    """
    file_path: str                                  # 文件完整路径
    name: str                                  # 文件名（含扩展名）
    size: int                                  # 文件大小（字节）
    modified_time: datetime                    # 修改时间
    created_time: datetime                     # 创建时间
    extension: str                             # 文件扩展名
    file_id: Optional[str] = None              # 文件唯一ID（数据库内部标识）
    is_video: bool = False                     # 是否为视频文件
    hash: Optional[str] = None                 # 文件哈希值（MD5）
    resolution: Optional[Tuple[int, int]] = None  # 视频分辨率（宽，高）
    bitrate: Optional[int] = None             # 视频比特率
    video_quality: Optional[Dict[str, Any]] = None  # 视频质量信息
    is_junk: bool = False                      # 是否为垃圾文件
    is_whitelist: bool = False                 # 是否为白名单文件
    file_type: Optional[str] = None            # 文件类型（如JP/CH/ENG）
    metadata: Dict[str, Any] = field(default_factory=dict)  # 其他元数据
    parent_id: Optional[str] = None  # 父节点唯一ID（用于文件树索引）
    depth: int = 1  # 新增：文件/文件夹的层级深度，根目录为1
    
    @classmethod
    def from_path(cls, file_path: Union[str, Path], depth: int = 1) -> 'FileInfo':
        """
        从文件路径创建文件信息对象
        
        参数:
            file_path: 文件路径
            depth: 当前深度，根目录为1
        返回:
            FileInfo对象
        """
        file_path = _normalize_path(str(file_path))
        path = Path(file_path)
        stats = path.stat()
        
        return cls(
            file_path=file_path,
            name=path.name,
            size=stats.st_size,
            modified_time=datetime.fromtimestamp(stats.st_mtime),
            created_time=datetime.fromtimestamp(stats.st_ctime),
            extension=path.suffix.lower(),
            depth=depth
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """
        将文件信息转换为字典
        返回:
            文件信息字典
        """
        result = {
            "path": self.file_path,  # 数据库期望 path 字段
            "file_path": self.file_path,  # 保持向后兼容
            "name": self.name,
            "size": self.size,
            "modified_time": self.modified_time.isoformat(),
            "created_time": self.created_time.isoformat(),
            "extension": self.extension,
            "is_video": self.is_video,
            "is_junk": self.is_junk,
            "is_whitelist": self.is_whitelist,
            "file_type": self.file_type,
            "depth": self.depth,
        }
        
        # 添加可选字段
        if self.file_id:
            result["file_id"] = self.file_id
        if self.hash:
            result["hash"] = self.hash
        if self.resolution:
            result["resolution"] = self.resolution
        if self.bitrate:
            result["bitrate"] = self.bitrate
        if self.video_quality:
            result["video_quality"] = self.video_quality
        if self.metadata:
            result["metadata"] = self.metadata
        if self.parent_id:
            result["parent_id"] = self.parent_id
            
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'FileInfo':
        """
        从字典创建文件信息对象
        参数:
            data: 文件信息字典
        返回:
            FileInfo对象
        """
        # 处理日期时间字段
        modified_time = data.get("modified_time")
        if isinstance(modified_time, str):
            modified_time = datetime.fromisoformat(modified_time)
        elif modified_time is None:
            modified_time = datetime.now()  # 默认使用当前时间
            
        created_time = data.get("created_time")
        if isinstance(created_time, str):
            created_time = datetime.fromisoformat(created_time)
        elif created_time is None:
            created_time = datetime.now()  # 默认使用当前时间
        
        # 处理路径字段，优先使用file_path，如果没有则使用path
        file_path = data.get("file_path") or data.get("path") or data.get("filepath", "")
        
        # 处理depth字段，默认1
        depth = data.get("depth", 1)
        
        # 创建对象
        file_info = cls(
            file_path=file_path,
            name=data.get("name", ""),
            size=data.get("size", 0),
            modified_time=modified_time,
            created_time=created_time,
            extension=data.get("extension", ""),
            file_id=data.get("file_id"),
            is_video=data.get("is_video", False),
            hash=data.get("hash"),
            resolution=data.get("resolution"),
            bitrate=data.get("bitrate"),
            video_quality=data.get("video_quality"),
            is_junk=data.get("is_junk", False),
            is_whitelist=data.get("is_whitelist", False),
            file_type=data.get("file_type"),
            metadata=data.get("metadata", {}),
            parent_id=data.get("parent_id"),
            depth=depth
        )
        
        return file_info


@dataclass
class VideoInfo:
    """
    视频信息模型
    
    存储视频文件的详细信息
    """
    file_info: FileInfo                        # 基本文件信息
    resolution: Tuple[int, int]                # 分辨率（宽，高）
    bitrate: int                               # 比特率
    duration: Optional[float] = None           # 时长（秒）
    codec: Optional[str] = None                # 编解码器
    frame_rate: Optional[float] = None         # 帧率
    quality_score: Optional[int] = None        # 质量评分
    
    def to_dict(self) -> Dict[str, Any]:
        """
        将视频信息转换为字典
        
        返回:
            视频信息字典
        """
        result = {
            "file_info": self.file_info.to_dict(),
            "resolution": self.resolution,
            "bitrate": self.bitrate,
        }
        
        # 添加可选字段
        if self.duration is not None:
            result["duration"] = self.duration
        if self.codec:
            result["codec"] = self.codec
        if self.frame_rate is not None:
            result["frame_rate"] = self.frame_rate
        if self.quality_score is not None:
            result["quality_score"] = self.quality_score
            
        return result


@dataclass
class ScanResult:
    """
    扫描结果模型
    
    存储文件扫描的结果信息
    """
    total_files: int = 0                       # 扫描的文件总数
    processed_files: int = 0                   # 处理的文件数
    video_files: int = 0                       # 视频文件数
    junk_files: int = 0                        # 垃圾文件数
    whitelist_files: int = 0                   # 白名单文件数
    duplicate_groups: int = 0                  # 重复文件组数
    duplicate_files: int = 0                   # 重复文件数
    same_name_video_groups: int = 0            # 同名视频组数
    same_name_videos: int = 0                  # 同名视频数
    total_size: int = 0                        # 所有文件总大小
    duplicate_size: int = 0                    # 重复文件总大小
    junk_size: int = 0                         # 垃圾文件总大小
    scan_time: float = 0.0                     # 扫描耗时（秒）
    error_count: int = 0                       # 错误数量
    file_types: Dict[str, int] = field(default_factory=dict)  # 文件类型统计
    db_inserted: int = 0                       # 数据库插入记录数
    db_updated: int = 0                        # 数据库更新记录数
    db_deleted: int = 0                        # 数据库删除记录数
    
    def to_dict(self) -> Dict[str, Any]:
        """
        将扫描结果转换为字典
        
        返回:
            扫描结果字典
        """
        return {
            "total_files": self.total_files,
            "processed_files": self.processed_files,
            "video_files": self.video_files,
            "junk_files": self.junk_files,
            "whitelist_files": self.whitelist_files,
            "duplicate_groups": self.duplicate_groups,
            "duplicate_files": self.duplicate_files,
            "same_name_video_groups": self.same_name_video_groups,
            "same_name_videos": self.same_name_videos,
            "total_size": self.total_size,
            "duplicate_size": self.duplicate_size,
            "junk_size": self.junk_size,
            "scan_time": self.scan_time,
            "error_count": self.error_count,
            "file_types": self.file_types,
            "db_inserted": self.db_inserted,
            "db_updated": self.db_updated,
            "db_deleted": self.db_deleted
        }