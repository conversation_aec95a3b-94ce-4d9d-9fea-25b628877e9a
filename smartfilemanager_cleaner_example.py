#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
智能文件管理器数据库清理示例

演示如何使用数据库清理工具来清理智能文件管理器项目的数据库。

作者: AI助手
日期: 2024-01-01
版本: 1.0.0
"""

import asyncio
import sys
from pathlib import Path

# 添加db_cleaner到路径
sys.path.insert(0, str(Path(__file__).parent))

from db_cleaner import create_cleaner, CleanerConfig, CleanerFactory
from db_cleaner.utils.logger import get_logger


async def example_1_quick_clean():
    """示例1: 快速清理智能文件管理器数据库"""
    print("=== 示例1: 快速清理 ===")
    
    try:
        # 创建清理器
        cleaner = create_cleaner(
            database_type="mongodb",
            connection_params={
                "host": "localhost",
                "port": 27017,
                "database": "smartfilemanager"
            }
        )
        
        # 执行清理
        async with cleaner:
            # 分析数据库状态
            print("分析数据库状态...")
            analysis = await cleaner.analyze_targets()
            
            print(f"发现 {len(analysis['targets'])} 个集合")
            print(f"总文档数: {analysis['total_documents']}")
            
            # 选择性清理主要集合
            targets = ["files", "folders"]
            print(f"清理目标: {targets}")
            
            # 确认清理
            confirm = input("确认清理？(y/N): ").lower() == 'y'
            
            if confirm:
                report = await cleaner.clean_selective(targets, confirm=True)
                print(report.get_summary())
            else:
                print("清理已取消")
                
    except Exception as e:
        print(f"清理失败: {e}")


async def example_2_config_based_clean():
    """示例2: 基于配置文件的清理"""
    print("\n=== 示例2: 配置文件清理 ===")
    
    config_file = "smartfilemanager_cleaner_config.yaml"
    
    if not Path(config_file).exists():
        print(f"配置文件不存在: {config_file}")
        return
    
    try:
        # 从配置文件创建清理器
        cleaner = CleanerFactory.create_cleaner_from_file(config_file)
        
        async with cleaner:
            # 发现数据库结构
            print("发现数据库结构...")
            schema = await cleaner.discover_database()
            
            print(f"数据库类型: {schema['database_info']['database_type']}")
            print(f"集合数量: {len(schema['collections'])}")
            
            # 显示每个集合的信息
            for name, info in schema['collections'].items():
                if 'error' not in info:
                    print(f"  {name}: {info['document_count']} 个文档")
                else:
                    print(f"  {name}: 错误 - {info['error']}")
            
            # 执行配置的清理规则
            print("\n执行清理规则...")
            confirm = input("确认执行所有清理规则？(y/N): ").lower() == 'y'
            
            if confirm:
                report = await cleaner.clean_all(confirm=True)
                print(report.get_summary())
                
                # 显示备份信息
                if report.backups_created:
                    print(f"\n创建的备份文件:")
                    for backup in report.backups_created:
                        print(f"  - {backup}")
            else:
                print("清理已取消")
                
    except Exception as e:
        print(f"配置文件清理失败: {e}")


async def example_3_selective_clean_with_conditions():
    """示例3: 带条件的选择性清理"""
    print("\n=== 示例3: 条件清理 ===")
    
    try:
        # 创建清理器
        config = CleanerConfig.from_file("smartfilemanager_cleaner_config.yaml")
        cleaner = CleanerFactory.create_cleaner(config)
        
        async with cleaner:
            # 示例：只清理特定条件的数据
            conditions = {
                # 示例条件：清理扫描状态为失败的文件
                # "scan_status": "failed"
                # 或者清理特定时间之前的数据
                # "created_time": {"$lt": "2024-01-01T00:00:00Z"}
            }
            
            print("执行条件清理...")
            print(f"清理条件: {conditions if conditions else '无条件（清理所有）'}")
            
            # 先备份
            targets = ["files", "folders"]
            print("创建备份...")
            backup_files = await cleaner.backup_targets(targets, "./manual_backup")
            
            print(f"备份完成: {len(backup_files)} 个文件")
            
            # 执行清理
            confirm = input("确认执行条件清理？(y/N): ").lower() == 'y'
            
            if confirm:
                report = await cleaner.clean_selective(
                    targets=targets,
                    conditions=conditions,
                    confirm=True
                )
                print(report.get_summary())
            else:
                print("清理已取消")
                
    except Exception as e:
        print(f"条件清理失败: {e}")


async def example_4_dry_run():
    """示例4: 试运行模式"""
    print("\n=== 示例4: 试运行模式 ===")
    
    try:
        # 创建配置并启用试运行模式
        config = CleanerConfig.from_file("smartfilemanager_cleaner_config.yaml")
        config.safety["dry_run"] = True
        
        cleaner = CleanerFactory.create_cleaner(config)
        
        async with cleaner:
            print("试运行模式 - 不会执行实际清理操作")
            
            # 分析将要清理的数据
            analysis = await cleaner.analyze_targets()
            
            print("将要清理的数据:")
            for target, info in analysis['targets'].items():
                if 'error' not in info:
                    count = info.get('document_count', 0)
                    print(f"  {target}: {count} 个文档")
                    
                    if count > 0:
                        # 显示样本数据
                        samples = info.get('sample_documents', [])
                        if samples:
                            print(f"    样本数据: {samples[0] if samples else 'N/A'}")
            
            print(f"\n总计将清理: {analysis['total_documents']} 个文档")
            print("注意: 这只是预览，实际清理需要关闭试运行模式")
            
    except Exception as e:
        print(f"试运行失败: {e}")


async def example_5_custom_progress_callback():
    """示例5: 自定义进度回调"""
    print("\n=== 示例5: 进度跟踪 ===")
    
    try:
        cleaner = create_cleaner(
            database_type="mongodb",
            connection_params={
                "host": "localhost",
                "port": 27017,
                "database": "smartfilemanager"
            }
        )
        
        # 定义进度回调函数
        async def progress_callback(target, deleted_count, duration):
            print(f"  ✓ {target}: 删除 {deleted_count} 条记录，耗时 {duration:.2f} 秒")
        
        # 定义确认回调函数
        async def confirmation_callback(message):
            return input(f"{message} (y/N): ").lower() == 'y'
        
        # 设置回调函数
        cleaner.set_progress_callback(progress_callback)
        cleaner.set_confirmation_callback(confirmation_callback)
        
        async with cleaner:
            print("执行带进度跟踪的清理...")
            
            targets = ["files", "folders"]
            report = await cleaner.clean_selective(targets)
            
            print("\n清理完成!")
            print(f"总操作数: {len(report.operations)}")
            print(f"总耗时: {report.statistics.get('duration', 0):.2f} 秒")
            
    except Exception as e:
        print(f"进度跟踪清理失败: {e}")


async def main():
    """主函数"""
    print("智能文件管理器数据库清理工具示例")
    print("=" * 50)
    
    # 设置日志
    logger = get_logger(level="INFO")
    
    examples = [
        ("快速清理", example_1_quick_clean),
        ("配置文件清理", example_2_config_based_clean),
        ("条件清理", example_3_selective_clean_with_conditions),
        ("试运行模式", example_4_dry_run),
        ("进度跟踪", example_5_custom_progress_callback),
    ]
    
    print("可用示例:")
    for i, (name, _) in enumerate(examples, 1):
        print(f"  {i}. {name}")
    
    print("  0. 运行所有示例")
    print("  q. 退出")
    
    while True:
        choice = input("\n请选择示例 (0-5, q): ").strip()
        
        if choice.lower() == 'q':
            print("退出")
            break
        elif choice == '0':
            print("运行所有示例...")
            for name, func in examples:
                print(f"\n{'='*20} {name} {'='*20}")
                try:
                    await func()
                except KeyboardInterrupt:
                    print("\n用户中断")
                    break
                except Exception as e:
                    print(f"示例执行失败: {e}")
            break
        elif choice.isdigit() and 1 <= int(choice) <= len(examples):
            idx = int(choice) - 1
            name, func = examples[idx]
            print(f"\n{'='*20} {name} {'='*20}")
            try:
                await func()
            except KeyboardInterrupt:
                print("\n用户中断")
            except Exception as e:
                print(f"示例执行失败: {e}")
        else:
            print("无效选择，请重试")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序执行失败: {e}")
        sys.exit(1)
