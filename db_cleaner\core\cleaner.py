#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库清理器核心类

提供数据库清理的核心功能，包括清理、备份、恢复等操作。

作者: AI助手
日期: 2024-01-01
版本: 1.0.0
"""

import asyncio
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Callable

from .config import CleanerConfig, CleanupRuleConfig
from ..adapters.base import DatabaseAdapter
from ..utils.logger import get_logger
from ..utils.exceptions import CleanupError, BackupError, ValidationError


class CleanupReport:
    """清理报告类"""
    
    def __init__(self):
        self.start_time = datetime.now()
        self.end_time = None
        self.success = False
        self.operations = []
        self.errors = []
        self.warnings = []
        self.statistics = {}
        self.backups_created = []
    
    def add_operation(self, operation: str, target: str, result: Any, 
                     duration: float = None, details: Dict[str, Any] = None):
        """添加操作记录"""
        self.operations.append({
            "operation": operation,
            "target": target,
            "result": result,
            "duration": duration,
            "details": details or {},
            "timestamp": datetime.now()
        })
    
    def add_error(self, error: str, target: str = None):
        """添加错误记录"""
        self.errors.append({
            "error": error,
            "target": target,
            "timestamp": datetime.now()
        })
    
    def add_warning(self, warning: str, target: str = None):
        """添加警告记录"""
        self.warnings.append({
            "warning": warning,
            "target": target,
            "timestamp": datetime.now()
        })
    
    def finish(self, success: bool = True):
        """完成报告"""
        self.end_time = datetime.now()
        self.success = success
        self.statistics["duration"] = (self.end_time - self.start_time).total_seconds()
        self.statistics["operations_count"] = len(self.operations)
        self.statistics["errors_count"] = len(self.errors)
        self.statistics["warnings_count"] = len(self.warnings)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "start_time": self.start_time.isoformat(),
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "success": self.success,
            "operations": self.operations,
            "errors": self.errors,
            "warnings": self.warnings,
            "statistics": self.statistics,
            "backups_created": self.backups_created
        }
    
    def get_summary(self) -> str:
        """获取报告摘要"""
        duration = self.statistics.get("duration", 0)
        status = "成功" if self.success else "失败"
        
        summary = f"""
=== 数据库清理报告 ===
开始时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}
结束时间: {self.end_time.strftime('%Y-%m-%d %H:%M:%S') if self.end_time else '未完成'}
执行状态: {status}
执行时长: {duration:.2f} 秒
操作数量: {len(self.operations)}
错误数量: {len(self.errors)}
警告数量: {len(self.warnings)}
"""
        
        if self.backups_created:
            summary += f"\n创建的备份:\n"
            for backup in self.backups_created:
                summary += f"  - {backup}\n"
        
        if self.errors:
            summary += f"\n错误详情:\n"
            for error in self.errors:
                summary += f"  - {error['error']}\n"
        
        if self.warnings:
            summary += f"\n警告信息:\n"
            for warning in self.warnings:
                summary += f"  - {warning['warning']}\n"
        
        return summary


class DatabaseCleaner:
    """数据库清理器"""
    
    def __init__(self, adapter: DatabaseAdapter, config: CleanerConfig):
        """
        初始化数据库清理器
        
        参数:
            adapter: 数据库适配器
            config: 清理配置
        """
        self.adapter = adapter
        self.config = config
        self.logger = get_logger()
        self.is_connected = False
        
        # 回调函数
        self.progress_callback: Optional[Callable] = None
        self.confirmation_callback: Optional[Callable] = None
    
    async def connect(self) -> bool:
        """连接到数据库"""
        try:
            success = await self.adapter.connect()
            self.is_connected = success
            
            if success:
                self.logger.info("数据库连接成功")
            else:
                self.logger.error("数据库连接失败")
            
            return success
            
        except Exception as e:
            self.logger.error(f"数据库连接异常: {e}")
            return False
    
    async def disconnect(self) -> None:
        """断开数据库连接"""
        try:
            await self.adapter.disconnect()
            self.is_connected = False
            self.logger.info("数据库连接已断开")
        except Exception as e:
            self.logger.error(f"断开数据库连接异常: {e}")
    
    async def test_connection(self) -> bool:
        """测试数据库连接"""
        return await self.adapter.test_connection()
    
    async def discover_database(self) -> Dict[str, Any]:
        """发现数据库结构"""
        if not self.is_connected:
            await self.connect()
        
        return await self.adapter.discover_schema()
    
    async def analyze_targets(self, targets: List[str] = None) -> Dict[str, Any]:
        """分析清理目标"""
        if not self.is_connected:
            await self.connect()
        
        # 如果没有指定目标，使用配置中的规则
        if targets is None:
            targets = [rule.target for rule in self.config.rules]
        
        # 如果仍然没有目标，获取所有集合
        if not targets:
            targets = await self.adapter.list_collections()
        
        analysis = {
            "targets": {},
            "total_documents": 0,
            "total_size": 0,
            "analyzed_at": datetime.now().isoformat()
        }
        
        for target in targets:
            try:
                target_analysis = await self.adapter.analyze_collection(target)
                analysis["targets"][target] = target_analysis
                analysis["total_documents"] += target_analysis.get("document_count", 0)
                
                # 累计大小（如果有的话）
                if "info" in target_analysis and "size" in target_analysis["info"]:
                    analysis["total_size"] += target_analysis["info"]["size"]
                    
            except Exception as e:
                self.logger.warning(f"分析目标 {target} 失败: {e}")
                analysis["targets"][target] = {"error": str(e)}
        
        return analysis
    
    async def clean_all(self, confirm: bool = None) -> CleanupReport:
        """清理所有配置的目标"""
        report = CleanupReport()
        
        try:
            if not self.is_connected:
                await self.connect()
            
            # 确认操作
            if confirm is None:
                confirm = not self.config.safety.get("confirm_cleanup", True)
            
            if not confirm and self.confirmation_callback:
                confirm = await self.confirmation_callback("确认清理所有配置的目标？")
            
            if not confirm:
                report.add_warning("用户取消了清理操作")
                report.finish(False)
                return report
            
            # 执行清理规则
            for rule in self.config.rules:
                await self._execute_cleanup_rule(rule, report)
            
            report.finish(True)
            self.logger.info("清理操作完成")
            
        except Exception as e:
            report.add_error(f"清理操作失败: {e}")
            report.finish(False)
            self.logger.error(f"清理操作失败: {e}")
        
        return report
    
    async def clean_selective(self, targets: List[str], 
                            conditions: Dict[str, Any] = None,
                            confirm: bool = None) -> CleanupReport:
        """选择性清理指定目标"""
        report = CleanupReport()
        
        try:
            if not self.is_connected:
                await self.connect()
            
            # 确认操作
            if confirm is None:
                confirm = not self.config.safety.get("confirm_cleanup", True)
            
            if not confirm and self.confirmation_callback:
                confirm = await self.confirmation_callback(f"确认清理目标: {targets}？")
            
            if not confirm:
                report.add_warning("用户取消了清理操作")
                report.finish(False)
                return report
            
            # 为每个目标创建临时规则
            for target in targets:
                rule = CleanupRuleConfig(
                    target=target,
                    conditions=conditions or {},
                    backup=self.config.backup.enabled,
                    operation="delete"
                )
                
                await self._execute_cleanup_rule(rule, report)
            
            report.finish(True)
            self.logger.info(f"选择性清理完成: {targets}")
            
        except Exception as e:
            report.add_error(f"选择性清理失败: {e}")
            report.finish(False)
            self.logger.error(f"选择性清理失败: {e}")
        
        return report
    
    async def backup_targets(self, targets: List[str], 
                           backup_dir: str = None) -> List[str]:
        """备份指定目标"""
        if not self.is_connected:
            await self.connect()
        
        if backup_dir is None:
            backup_dir = self.config.backup.directory
        
        backup_files = []
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        for target in targets:
            try:
                backup_file = Path(backup_dir) / f"{target}_{timestamp}.{self.config.backup.format}"
                
                result = await self.adapter.backup_collection(
                    target,
                    str(backup_file),
                    format=self.config.backup.format
                )
                
                backup_files.append(result)
                self.logger.info(f"备份完成: {target} -> {result}")
                
            except Exception as e:
                self.logger.error(f"备份失败 {target}: {e}")
                raise BackupError(f"备份失败: {e}", backup_path=str(backup_file))
        
        return backup_files
    
    async def _execute_cleanup_rule(self, rule: CleanupRuleConfig, 
                                  report: CleanupReport) -> None:
        """执行清理规则"""
        target = rule.target
        start_time = time.time()
        
        try:
            # 检查目标是否存在
            collections = await self.adapter.list_collections()
            if target not in collections:
                report.add_warning(f"目标不存在: {target}", target)
                return
            
            # 统计清理前的数据
            before_count = await self.adapter.count_documents(target, rule.conditions)
            
            # 检查安全限制
            max_delete = self.config.safety.get("max_delete_count", 10000)
            if before_count > max_delete:
                report.add_error(
                    f"删除数量 {before_count} 超过安全限制 {max_delete}",
                    target
                )
                return
            
            # 备份（如果需要）
            backup_file = None
            if rule.backup and self.config.backup.enabled:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_dir = Path(self.config.backup.directory)
                backup_file = backup_dir / f"{target}_{timestamp}.{self.config.backup.format}"
                
                await self.adapter.backup_collection(
                    target,
                    str(backup_file),
                    conditions=rule.conditions,
                    format=self.config.backup.format
                )
                
                report.backups_created.append(str(backup_file))
                self.logger.info(f"备份创建: {backup_file}")
            
            # 执行清理
            if rule.operation == "delete":
                deleted_count = await self.adapter.delete_documents(
                    target,
                    rule.conditions,
                    rule.batch_size
                )
            elif rule.operation == "truncate":
                await self.adapter.truncate_collection(target)
                deleted_count = before_count
            else:
                raise ValidationError(f"不支持的清理操作: {rule.operation}")
            
            # 统计清理后的数据
            after_count = await self.adapter.count_documents(target, rule.conditions)
            
            duration = time.time() - start_time
            
            # 记录操作结果
            report.add_operation(
                operation=rule.operation,
                target=target,
                result=deleted_count,
                duration=duration,
                details={
                    "before_count": before_count,
                    "after_count": after_count,
                    "backup_file": str(backup_file) if backup_file else None,
                    "conditions": rule.conditions
                }
            )
            
            self.logger.info(
                f"清理完成: {target}, 删除 {deleted_count} 条记录, "
                f"耗时 {duration:.2f} 秒"
            )
            
            # 进度回调
            if self.progress_callback:
                await self.progress_callback(target, deleted_count, duration)
                
        except Exception as e:
            duration = time.time() - start_time
            report.add_error(f"清理失败: {e}", target)
            self.logger.error(f"清理目标 {target} 失败: {e}")
    
    def set_progress_callback(self, callback: Callable):
        """设置进度回调函数"""
        self.progress_callback = callback
    
    def set_confirmation_callback(self, callback: Callable):
        """设置确认回调函数"""
        self.confirmation_callback = callback
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.disconnect()
