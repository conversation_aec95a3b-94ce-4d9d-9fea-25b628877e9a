#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Database Cleaner - 通用数据库清理工具

一个强大、灵活、可重用的数据库清理工具，支持多种数据库类型和清理策略。

主要功能:
- 支持多种数据库类型（MongoDB、MySQL、PostgreSQL等）
- 提供CLI和API两种使用方式
- 灵活的配置系统和清理规则
- 安全确认机制和回滚功能
- 定时清理和批量清理任务
- 数据库结构自动发现

作者: AI助手
版本: 1.0.0
许可: MIT License
"""

__version__ = "1.0.0"
__author__ = "AI Assistant"
__license__ = "MIT"

# 导入核心类和函数
from .core.cleaner import DatabaseCleaner
from .core.config import CleanerConfig
from .core.factory import CleanerFactory
from .adapters.base import DatabaseAdapter
from .adapters.mongodb import MongoDBAdapter
from .adapters.mysql import MySQLAdapter
from .adapters.postgresql import PostgreSQLAdapter
from .utils.logger import get_logger
from .utils.exceptions import (
    CleanerError,
    ConfigurationError,
    DatabaseConnectionError,
    CleanupError
)

# 便捷的工厂函数
def create_cleaner(config_path=None, **kwargs):
    """
    创建数据库清理器实例的便捷函数
    
    参数:
        config_path: 配置文件路径
        **kwargs: 额外的配置参数
    
    返回:
        DatabaseCleaner实例
    """
    config = CleanerConfig.from_file(config_path) if config_path else CleanerConfig(**kwargs)
    return CleanerFactory.create_cleaner(config)

def quick_clean(database_type, connection_params, collections=None, confirm=False):
    """
    快速清理数据库的便捷函数
    
    参数:
        database_type: 数据库类型 ('mongodb', 'mysql', 'postgresql')
        connection_params: 数据库连接参数
        collections: 要清理的集合/表列表，None表示全部
        confirm: 是否确认执行清理
    
    返回:
        清理报告
    """
    config = CleanerConfig(
        database_type=database_type,
        connection=connection_params,
        targets=collections or [],
        auto_confirm=confirm
    )
    
    cleaner = CleanerFactory.create_cleaner(config)
    return cleaner.clean_all() if not collections else cleaner.clean_selective(collections)

# 导出主要接口
__all__ = [
    'DatabaseCleaner',
    'CleanerConfig', 
    'CleanerFactory',
    'DatabaseAdapter',
    'MongoDBAdapter',
    'MySQLAdapter',
    'PostgreSQLAdapter',
    'create_cleaner',
    'quick_clean',
    'get_logger',
    'CleanerError',
    'ConfigurationError',
    'DatabaseConnectionError',
    'CleanupError'
]
