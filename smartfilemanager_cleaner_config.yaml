# 智能文件管理器专用数据库清理配置
# 该配置文件专门为智能文件管理器项目设计，包含所有相关集合的清理规则

# 数据库连接配置
database:
  type: mongodb
  host: localhost
  port: 27017
  database: smartfilemanager
  # 如果需要认证，取消注释以下行
  # username: your_username
  # password: your_password
  
  # MongoDB连接选项
  options:
    serverSelectionTimeoutMS: 5000
    connectTimeoutMS: 5000
    maxPoolSize: 10

# 清理规则配置
rules:
  # 清理文件信息集合
  - target: files
    conditions: {}  # 清理所有文件记录
    backup: true
    operation: delete
    batch_size: 1000
    timeout: 120
  
  # 清理文件夹信息集合
  - target: folders
    conditions: {}  # 清理所有文件夹记录
    backup: true
    operation: delete
    batch_size: 500
    timeout: 120
  
  # 清理树节点缓存（如果存在）
  - target: tree_nodes
    conditions: {}
    backup: false  # 缓存数据不需要备份
    operation: delete
    batch_size: 2000
    timeout: 60
  
  # 清理文件索引（如果存在）
  - target: file_index
    conditions: {}
    backup: false
    operation: delete
    batch_size: 2000
    timeout: 60
  
  # 清理缓存数据（如果存在）
  - target: cache_data
    conditions: {}
    backup: false
    operation: delete
    batch_size: 5000
    timeout: 30
  
  # 清理临时数据（如果存在）
  - target: temp_data
    conditions: {}
    backup: false
    operation: delete
    batch_size: 5000
    timeout: 30
  
  # 清理兼容性测试数据（如果存在）
  - target: compat_test_db
    conditions: {}
    backup: false
    operation: delete
    batch_size: 1000
    timeout: 30

# 备份配置
backup:
  enabled: true
  directory: "./database_backups"
  format: json
  compress: true
  keep_count: 10  # 保留最近10个备份

# 定时任务配置（可选）
schedule:
  enabled: false
  # 每周日凌晨3点执行清理
  cron: "0 3 * * 0"

# 日志配置
logging:
  level: INFO
  file: "./logs/smartfilemanager_cleaner.log"
  color: true
  format: "%(asctime)s [%(levelname)s] [%(name)s] %(message)s"

# 安全设置
safety:
  confirm_cleanup: true        # 需要确认清理操作
  dry_run: false              # 设置为true可以预览清理操作
  protected_collections: []   # 受保护的集合（不会被清理）
  max_delete_count: 50000     # 单次最大删除数量

# 高级选项
options:
  parallel_operations: false   # 是否并行执行操作
  progress_reporting: true     # 是否报告进度
  auto_optimize: true         # 清理后是否自动优化数据库
  
  # 自定义清理策略
  custom_strategies:
    # 按时间清理策略（示例）
    time_based_cleanup:
      enabled: false
      older_than_days: 30
      collections: ["temp_data", "cache_data"]
    
    # 按大小清理策略（示例）
    size_based_cleanup:
      enabled: false
      max_collection_size_mb: 1000
      collections: ["files", "folders"]
