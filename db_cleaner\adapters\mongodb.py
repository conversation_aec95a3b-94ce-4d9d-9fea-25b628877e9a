#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MongoDB数据库适配器

实现了MongoDB数据库的清理操作。

作者: AI助手
日期: 2024-01-01
版本: 1.0.0
"""

import json
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path

try:
    import motor.motor_asyncio
    from pymongo import MongoClient
    from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError
    MONGODB_AVAILABLE = True
except ImportError:
    MONGODB_AVAILABLE = False

from .base import DatabaseAdapter
from ..utils.exceptions import DatabaseConnectionError, CleanupError, ConfigurationError


class MongoDBAdapter(DatabaseAdapter):
    """MongoDB数据库适配器"""
    
    def __init__(self, connection_params: Dict[str, Any]):
        """初始化MongoDB适配器"""
        if not MONGODB_AVAILABLE:
            raise ConfigurationError("MongoDB依赖未安装，请运行: pip install motor pymongo")
        
        super().__init__(connection_params)
        self.client = None
        self.async_client = None
        self.database = None
        self.database_name = connection_params.get('database', 'test')
    
    async def connect(self) -> bool:
        """连接到MongoDB"""
        try:
            # 构建连接URI
            if 'uri' in self.connection_params:
                uri = self.connection_params['uri']
            else:
                host = self.connection_params.get('host', 'localhost')
                port = self.connection_params.get('port', 27017)
                username = self.connection_params.get('username')
                password = self.connection_params.get('password')
                
                if username and password:
                    uri = f"mongodb://{username}:{password}@{host}:{port}/"
                else:
                    uri = f"mongodb://{host}:{port}/"
            
            # 创建异步客户端
            self.async_client = motor.motor_asyncio.AsyncIOMotorClient(
                uri,
                serverSelectionTimeoutMS=5000,
                connectTimeoutMS=5000
            )
            
            # 创建同步客户端（用于某些操作）
            self.client = MongoClient(
                uri,
                serverSelectionTimeoutMS=5000,
                connectTimeoutMS=5000
            )
            
            # 测试连接
            await self.async_client.admin.command('ping')
            
            # 获取数据库
            self.database = self.async_client[self.database_name]
            
            self.is_connected = True
            return True
            
        except Exception as e:
            raise DatabaseConnectionError(
                f"连接MongoDB失败: {e}",
                database_type="mongodb",
                connection_params=self.connection_params
            )
    
    async def disconnect(self) -> None:
        """断开MongoDB连接"""
        if self.async_client:
            self.async_client.close()
        
        if self.client:
            self.client.close()
        
        self.is_connected = False
    
    async def test_connection(self) -> bool:
        """测试MongoDB连接"""
        try:
            if not self.is_connected:
                return False
            
            await self.async_client.admin.command('ping')
            return True
            
        except Exception:
            return False
    
    async def get_database_info(self) -> Dict[str, Any]:
        """获取MongoDB数据库信息"""
        self._validate_connection()
        
        try:
            # 获取服务器信息
            server_info = await self.async_client.server_info()
            
            # 获取数据库统计信息
            db_stats = await self.database.command('dbStats')
            
            return {
                "database_type": "mongodb",
                "server_version": server_info.get('version'),
                "database_name": self.database_name,
                "collections_count": len(await self.database.list_collection_names()),
                "data_size": db_stats.get('dataSize', 0),
                "storage_size": db_stats.get('storageSize', 0),
                "index_size": db_stats.get('indexSize', 0),
                "objects": db_stats.get('objects', 0)
            }
            
        except Exception as e:
            raise CleanupError(f"获取数据库信息失败: {e}")
    
    async def list_collections(self) -> List[str]:
        """列出所有集合"""
        self._validate_connection()
        
        try:
            return await self.database.list_collection_names()
        except Exception as e:
            raise CleanupError(f"列出集合失败: {e}")
    
    async def get_collection_info(self, collection_name: str) -> Dict[str, Any]:
        """获取集合信息"""
        self._validate_connection()
        
        try:
            collection = self.database[collection_name]
            
            # 获取集合统计信息
            stats = await self.database.command('collStats', collection_name)
            
            return {
                "name": collection_name,
                "count": stats.get('count', 0),
                "size": stats.get('size', 0),
                "storage_size": stats.get('storageSize', 0),
                "total_index_size": stats.get('totalIndexSize', 0),
                "avg_obj_size": stats.get('avgObjSize', 0),
                "indexes": stats.get('nindexes', 0)
            }
            
        except Exception as e:
            raise CleanupError(
                f"获取集合信息失败: {e}",
                operation="get_collection_info",
                target=collection_name
            )
    
    async def count_documents(self, collection_name: str, 
                            conditions: Dict[str, Any] = None) -> int:
        """统计文档数量"""
        self._validate_connection()
        
        try:
            collection = self.database[collection_name]
            
            if conditions:
                return await collection.count_documents(conditions)
            else:
                return await collection.estimated_document_count()
                
        except Exception as e:
            raise CleanupError(
                f"统计文档数量失败: {e}",
                operation="count_documents",
                target=collection_name
            )
    
    async def delete_documents(self, collection_name: str, 
                             conditions: Dict[str, Any] = None,
                             batch_size: int = 1000) -> int:
        """删除文档"""
        self._validate_connection()
        
        try:
            collection = self.database[collection_name]
            
            if conditions is None:
                # 删除所有文档
                result = await collection.delete_many({})
                return result.deleted_count
            else:
                # 按条件删除
                total_deleted = 0
                
                while True:
                    # 批量删除
                    result = await collection.delete_many(conditions)
                    deleted_count = result.deleted_count
                    total_deleted += deleted_count
                    
                    # 如果删除数量小于批处理大小，说明已经删除完毕
                    if deleted_count < batch_size:
                        break
                    
                    # 避免阻塞事件循环
                    await asyncio.sleep(0.01)
                
                return total_deleted
                
        except Exception as e:
            raise CleanupError(
                f"删除文档失败: {e}",
                operation="delete_documents",
                target=collection_name
            )
    
    async def truncate_collection(self, collection_name: str) -> bool:
        """清空集合"""
        self._validate_connection()
        
        try:
            collection = self.database[collection_name]
            result = await collection.delete_many({})
            return True
            
        except Exception as e:
            raise CleanupError(
                f"清空集合失败: {e}",
                operation="truncate_collection",
                target=collection_name
            )
    
    async def backup_collection(self, collection_name: str, 
                               backup_path: str,
                               conditions: Dict[str, Any] = None,
                               format: str = "json") -> str:
        """备份集合数据"""
        self._validate_connection()
        
        try:
            collection = self.database[collection_name]
            backup_file = Path(backup_path)
            backup_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 构建查询条件
            query = conditions or {}
            
            if format.lower() == "json":
                # JSON格式备份
                documents = []
                async for doc in collection.find(query):
                    # 转换ObjectId为字符串
                    if '_id' in doc:
                        doc['_id'] = str(doc['_id'])
                    documents.append(doc)
                
                with open(backup_file, 'w', encoding='utf-8') as f:
                    json.dump(documents, f, indent=2, ensure_ascii=False, default=str)
            
            else:
                raise CleanupError(f"不支持的备份格式: {format}")
            
            return str(backup_file)
            
        except Exception as e:
            raise CleanupError(
                f"备份集合失败: {e}",
                operation="backup_collection",
                target=collection_name
            )
    
    async def restore_collection(self, collection_name: str, 
                               backup_path: str,
                               format: str = "json") -> bool:
        """恢复集合数据"""
        self._validate_connection()
        
        try:
            collection = self.database[collection_name]
            backup_file = Path(backup_path)
            
            if not backup_file.exists():
                raise CleanupError(f"备份文件不存在: {backup_path}")
            
            if format.lower() == "json":
                # JSON格式恢复
                with open(backup_file, 'r', encoding='utf-8') as f:
                    documents = json.load(f)
                
                if documents:
                    await collection.insert_many(documents)
            
            else:
                raise CleanupError(f"不支持的恢复格式: {format}")
            
            return True
            
        except Exception as e:
            raise CleanupError(
                f"恢复集合失败: {e}",
                operation="restore_collection",
                target=collection_name
            )
    
    async def get_sample_documents(self, collection_name: str, 
                                 limit: int = 5) -> List[Dict[str, Any]]:
        """获取样本文档"""
        self._validate_connection()
        
        try:
            collection = self.database[collection_name]
            documents = []
            
            async for doc in collection.find().limit(limit):
                # 转换ObjectId为字符串
                if '_id' in doc:
                    doc['_id'] = str(doc['_id'])
                documents.append(doc)
            
            return documents
            
        except Exception as e:
            raise CleanupError(
                f"获取样本文档失败: {e}",
                operation="get_sample_documents",
                target=collection_name
            )
    
    async def get_indexes(self, collection_name: str) -> List[Dict[str, Any]]:
        """获取集合索引信息"""
        self._validate_connection()
        
        try:
            collection = self.database[collection_name]
            indexes = []
            
            async for index in collection.list_indexes():
                indexes.append(index)
            
            return indexes
            
        except Exception as e:
            raise CleanupError(
                f"获取索引信息失败: {e}",
                operation="get_indexes",
                target=collection_name
            )
