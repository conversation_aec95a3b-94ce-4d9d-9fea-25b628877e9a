﻿# 测试框架
pytest>=8.2,<9.0  # 升级以兼容pytest-asyncio 1.x
pytest-cov==4.1.0
pytest-mock==3.11.1
locust==2.15.1

# 环境管理
python-dotenv>=1.0.0

# CI/CD工具
coverage>=7.3.0

# 核心依赖
numpy>=2.1.0  # 适配Python 3.13
python-dateutil>=2.8.2
PyYAML>=6.0,<7.0
opencv-python>=4.8.0  # 确保与numpy 2.x兼容
pyinstaller>=6.10.0,<7.0.0

# GUI相关
pywinauto>=0.6.8,<1.0.0
tk>=0.1.0
Pillow>=10.0.0
customtkinter==5.2.2

# 工具库
requests>=2.31.0,<3.0.0
tqdm>=4.66.0
watchdog>=3.0.0
python-magic>=0.4.27
send2trash>=1.8.3
python-magic-bin>=0.4.14; sys_platform == "win32"

# 数据库
pymongo>=4.6.0,<5.0.0
motor>=3.3.2,<4.0.0

# 异步支持
aiofiles==23.2.1
asyncio==3.4.3
aiodns>=3.1.1
aiohttp==3.9.5
aios==0.1
pytest-asyncio>=1.0.0

# 系统监控
psutil>=5.9.0

# 类型检查
mypy>=1.8.0
types-PyYAML>=6.0.0
types-requests>=2.31.0
ffmpeg-python>=0.2.0
pytest-json-report>=1.5.0
pytest-metadata>=3.1.1
pytest-timeout>=2.4.0
# 可选：常见依赖补充，提升兼容性
attrs>=25.3.0
multidict>=6.0.0
frozenlist>=1.0.0
idna>=3.0.0
chardet>=5.0.0
charset-normalizer>=3.0.0
pluggy>=1.0.0
iniconfig>=2.0.0
mypy_extensions>=1.0.0
packaging>=25.0
pathspec>=0.12.0
