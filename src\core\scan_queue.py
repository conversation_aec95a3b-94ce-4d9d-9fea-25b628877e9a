#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
ScanQueue - 扫描与写库解耦的队列模块
支持异步和同步接口，线程/协程安全。
"""
import asyncio
import threading
from queue import Queue, Empty
from typing import Optional, Callable
from src.core.progress_tracker import ProgressTracker

class ScanQueue:
    def __init__(self, maxsize=0):
        self._async_queue = asyncio.Queue(maxsize=maxsize)
        self._sync_queue = Queue(maxsize=maxsize)
        self._lock = threading.Lock()

    # 异步接口
    async def put_async(self, item):
        await self._async_queue.put(item)
        with self._lock:
            self._sync_queue.put(item)

    async def get_async(self):
        item = await self._async_queue.get()
        return item

    # 同步接口
    def put(self, item):
        with self._lock:
            self._sync_queue.put(item)
        # put到async_queue（兼容异步消费）
        loop = None
        try:
            loop = asyncio.get_running_loop()
        except RuntimeError:
            loop = None
        if loop and loop.is_running():
            # 如果在事件循环中，直接put_nowait
            self._async_queue.put_nowait(item)
        else:
            # 否则用线程安全的方式
            asyncio.run(self._async_queue.put(item))

    def get(self, timeout=None):
        try:
            return self._sync_queue.get(timeout=timeout)
        except Empty:
            return None

    def qsize(self):
        return self._sync_queue.qsize()

    def empty(self):
        return self._sync_queue.empty()

    def clear(self):
        with self._lock:
            while not self._sync_queue.empty():
                self._sync_queue.get()
        # 清空异步队列（不常用）
        while not self._async_queue.empty():
            self._async_queue.get_nowait()

    async def batch_put_async(self, items: list, progress_callback: Optional[Callable] = None):
        tracker = ProgressTracker(total_items=len(items), stages=["批量入队"])
        for i, item in enumerate(items):
            await self.put_async(item)
            tracker.update(1)
            progress_info = tracker.get_progress_info()
            if progress_callback:
                await asyncio.get_event_loop().run_in_executor(
                    None,
                    lambda: progress_callback(
                        progress_info["progress"],
                        f"批量入队: {i+1}/{len(items)}",
                        str(item),
                        i+1,
                        len(items),
                        progress_info["elapsed_time"]
                    )
                )

    async def batch_get_async(self, count: int, progress_callback: Optional[Callable] = None):
        tracker = ProgressTracker(total_items=count, stages=["批量出队"])
        items = []
        for i in range(count):
            item = await self.get_async()
            items.append(item)
            tracker.update(1)
            progress_info = tracker.get_progress_info()
            if progress_callback:
                await asyncio.get_event_loop().run_in_executor(
                    None,
                    lambda: progress_callback(
                        progress_info["progress"],
                        f"批量出队: {i+1}/{count}",
                        str(item),
                        i+1,
                        count,
                        progress_info["elapsed_time"]
                    )
                )
        return items 