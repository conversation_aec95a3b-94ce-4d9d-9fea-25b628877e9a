#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MySQL数据库适配器

实现了MySQL数据库的清理操作。

作者: AI助手
日期: 2024-01-01
版本: 1.0.0
"""

import json
import csv
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path

try:
    import aiomysql
    import pymysql
    MYSQL_AVAILABLE = True
except ImportError:
    MYSQL_AVAILABLE = False

from .base import DatabaseAdapter
from ..utils.exceptions import DatabaseConnectionError, CleanupError, ConfigurationError


class MySQLAdapter(DatabaseAdapter):
    """MySQL数据库适配器"""
    
    def __init__(self, connection_params: Dict[str, Any]):
        """初始化MySQL适配器"""
        if not MYSQL_AVAILABLE:
            raise ConfigurationError("MySQL依赖未安装，请运行: pip install aiomysql pymysql")
        
        super().__init__(connection_params)
        self.pool = None
        self.database_name = connection_params.get('database', 'test')
    
    async def connect(self) -> bool:
        """连接到MySQL"""
        try:
            # 创建连接池
            self.pool = await aiomysql.create_pool(
                host=self.connection_params.get('host', 'localhost'),
                port=self.connection_params.get('port', 3306),
                user=self.connection_params.get('username', 'root'),
                password=self.connection_params.get('password', ''),
                db=self.database_name,
                charset='utf8mb4',
                autocommit=True,
                maxsize=10,
                minsize=1
            )
            
            # 测试连接
            async with self.pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute("SELECT 1")
            
            self.is_connected = True
            return True
            
        except Exception as e:
            raise DatabaseConnectionError(
                f"连接MySQL失败: {e}",
                database_type="mysql",
                connection_params=self.connection_params
            )
    
    async def disconnect(self) -> None:
        """断开MySQL连接"""
        if self.pool:
            self.pool.close()
            await self.pool.wait_closed()
        
        self.is_connected = False
    
    async def test_connection(self) -> bool:
        """测试MySQL连接"""
        try:
            if not self.is_connected or not self.pool:
                return False
            
            async with self.pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute("SELECT 1")
                    return True
                    
        except Exception:
            return False
    
    async def get_database_info(self) -> Dict[str, Any]:
        """获取MySQL数据库信息"""
        self._validate_connection()
        
        try:
            async with self.pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    # 获取版本信息
                    await cursor.execute("SELECT VERSION()")
                    version = (await cursor.fetchone())[0]
                    
                    # 获取数据库大小
                    await cursor.execute("""
                        SELECT 
                            ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
                        FROM information_schema.tables 
                        WHERE table_schema = %s
                    """, (self.database_name,))
                    
                    size_result = await cursor.fetchone()
                    size_mb = size_result[0] if size_result and size_result[0] else 0
                    
                    # 获取表数量
                    await cursor.execute("""
                        SELECT COUNT(*) 
                        FROM information_schema.tables 
                        WHERE table_schema = %s
                    """, (self.database_name,))
                    
                    table_count = (await cursor.fetchone())[0]
                    
                    return {
                        "database_type": "mysql",
                        "server_version": version,
                        "database_name": self.database_name,
                        "tables_count": table_count,
                        "size_mb": float(size_mb)
                    }
                    
        except Exception as e:
            raise CleanupError(f"获取数据库信息失败: {e}")
    
    async def list_collections(self) -> List[str]:
        """列出所有表"""
        self._validate_connection()
        
        try:
            async with self.pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute("""
                        SELECT table_name 
                        FROM information_schema.tables 
                        WHERE table_schema = %s
                    """, (self.database_name,))
                    
                    tables = await cursor.fetchall()
                    return [table[0] for table in tables]
                    
        except Exception as e:
            raise CleanupError(f"列出表失败: {e}")
    
    async def get_collection_info(self, collection_name: str) -> Dict[str, Any]:
        """获取表信息"""
        self._validate_connection()
        
        try:
            async with self.pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    # 获取表统计信息
                    await cursor.execute("""
                        SELECT 
                            table_rows,
                            data_length,
                            index_length,
                            avg_row_length
                        FROM information_schema.tables 
                        WHERE table_schema = %s AND table_name = %s
                    """, (self.database_name, collection_name))
                    
                    result = await cursor.fetchone()
                    
                    if result:
                        return {
                            "name": collection_name,
                            "count": result[0] or 0,
                            "data_size": result[1] or 0,
                            "index_size": result[2] or 0,
                            "avg_row_size": result[3] or 0
                        }
                    else:
                        raise CleanupError(f"表不存在: {collection_name}")
                        
        except Exception as e:
            raise CleanupError(
                f"获取表信息失败: {e}",
                operation="get_collection_info",
                target=collection_name
            )
    
    async def count_documents(self, collection_name: str, 
                            conditions: Dict[str, Any] = None) -> int:
        """统计记录数量"""
        self._validate_connection()
        
        try:
            async with self.pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    if conditions:
                        # 构建WHERE子句
                        where_clause, params = self._build_where_clause(conditions)
                        sql = f"SELECT COUNT(*) FROM `{collection_name}` WHERE {where_clause}"
                        await cursor.execute(sql, params)
                    else:
                        sql = f"SELECT COUNT(*) FROM `{collection_name}`"
                        await cursor.execute(sql)
                    
                    result = await cursor.fetchone()
                    return result[0] if result else 0
                    
        except Exception as e:
            raise CleanupError(
                f"统计记录数量失败: {e}",
                operation="count_documents",
                target=collection_name
            )
    
    async def delete_documents(self, collection_name: str, 
                             conditions: Dict[str, Any] = None,
                             batch_size: int = 1000) -> int:
        """删除记录"""
        self._validate_connection()
        
        try:
            async with self.pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    if conditions is None:
                        # 删除所有记录
                        sql = f"DELETE FROM `{collection_name}`"
                        await cursor.execute(sql)
                        return cursor.rowcount
                    else:
                        # 按条件删除
                        where_clause, params = self._build_where_clause(conditions)
                        sql = f"DELETE FROM `{collection_name}` WHERE {where_clause} LIMIT {batch_size}"
                        
                        total_deleted = 0
                        while True:
                            await cursor.execute(sql, params)
                            deleted_count = cursor.rowcount
                            total_deleted += deleted_count
                            
                            if deleted_count < batch_size:
                                break
                        
                        return total_deleted
                        
        except Exception as e:
            raise CleanupError(
                f"删除记录失败: {e}",
                operation="delete_documents",
                target=collection_name
            )
    
    async def truncate_collection(self, collection_name: str) -> bool:
        """清空表"""
        self._validate_connection()
        
        try:
            async with self.pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    sql = f"TRUNCATE TABLE `{collection_name}`"
                    await cursor.execute(sql)
                    return True
                    
        except Exception as e:
            raise CleanupError(
                f"清空表失败: {e}",
                operation="truncate_collection",
                target=collection_name
            )
    
    async def backup_collection(self, collection_name: str, 
                               backup_path: str,
                               conditions: Dict[str, Any] = None,
                               format: str = "json") -> str:
        """备份表数据"""
        self._validate_connection()
        
        try:
            backup_file = Path(backup_path)
            backup_file.parent.mkdir(parents=True, exist_ok=True)
            
            async with self.pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    # 构建查询SQL
                    if conditions:
                        where_clause, params = self._build_where_clause(conditions)
                        sql = f"SELECT * FROM `{collection_name}` WHERE {where_clause}"
                        await cursor.execute(sql, params)
                    else:
                        sql = f"SELECT * FROM `{collection_name}`"
                        await cursor.execute(sql)
                    
                    rows = await cursor.fetchall()
                    
                    if format.lower() == "json":
                        # JSON格式备份
                        with open(backup_file, 'w', encoding='utf-8') as f:
                            json.dump(rows, f, indent=2, ensure_ascii=False, default=str)
                    
                    elif format.lower() == "csv":
                        # CSV格式备份
                        if rows:
                            with open(backup_file, 'w', newline='', encoding='utf-8') as f:
                                writer = csv.DictWriter(f, fieldnames=rows[0].keys())
                                writer.writeheader()
                                writer.writerows(rows)
                    
                    else:
                        raise CleanupError(f"不支持的备份格式: {format}")
            
            return str(backup_file)
            
        except Exception as e:
            raise CleanupError(
                f"备份表失败: {e}",
                operation="backup_collection",
                target=collection_name
            )
    
    async def restore_collection(self, collection_name: str, 
                               backup_path: str,
                               format: str = "json") -> bool:
        """恢复表数据"""
        # MySQL的恢复操作比较复杂，需要知道表结构
        # 这里提供基础实现，实际使用时可能需要根据具体需求调整
        raise CleanupError("MySQL恢复功能需要根据具体表结构实现")
    
    def _build_where_clause(self, conditions: Dict[str, Any]) -> tuple:
        """构建WHERE子句"""
        if not conditions:
            return "1=1", []
        
        clauses = []
        params = []
        
        for key, value in conditions.items():
            if isinstance(value, (list, tuple)):
                placeholders = ','.join(['%s'] * len(value))
                clauses.append(f"`{key}` IN ({placeholders})")
                params.extend(value)
            else:
                clauses.append(f"`{key}` = %s")
                params.append(value)
        
        return " AND ".join(clauses), params
