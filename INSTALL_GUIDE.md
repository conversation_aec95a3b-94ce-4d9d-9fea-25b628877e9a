# Database Cleaner 安装和使用指南

## 🚀 快速安装

### 1. 基础安装

```bash
# 克隆或复制 db_cleaner 目录到你的项目中
cp -r db_cleaner /path/to/your/project/

# 安装依赖
pip install pyyaml toml
```

### 2. 数据库驱动安装

根据你使用的数据库类型安装对应的驱动：

```bash
# MongoDB
pip install motor pymongo

# MySQL  
pip install aiomysql pymysql

# PostgreSQL
pip install asyncpg

# 或者安装所有驱动
pip install motor pymongo aiomysql pymysql asyncpg
```

### 3. 开发环境安装（可选）

```bash
# 安装开发依赖
pip install pytest pytest-asyncio pytest-cov black flake8 mypy

# 安装为可编辑包
pip install -e .
```

## 📖 智能文件管理器项目使用指南

### 1. 配置文件设置

使用提供的配置文件模板：

```bash
# 复制配置文件
cp smartfilemanager_cleaner_config.yaml config.yaml

# 根据需要编辑配置
nano config.yaml
```

主要配置项：
- `database.host`: 数据库主机地址
- `database.port`: 数据库端口
- `database.database`: 数据库名称
- `rules`: 清理规则列表

### 2. 命令行使用

#### 快速清理
```bash
# 清理指定集合
python -m db_cleaner.cli clean --type mongodb --host localhost --database smartfilemanager --collections files,folders --confirm

# 使用配置文件清理
python -m db_cleaner.cli clean --config config.yaml
```

#### 分析数据库
```bash
# 分析数据库结构
python -m db_cleaner.cli analyze --config config.yaml

# 保存分析结果
python -m db_cleaner.cli analyze --config config.yaml --output analysis.json
```

#### 备份数据
```bash
# 备份指定集合
python -m db_cleaner.cli backup --config config.yaml --collections files,folders --output ./backup
```

### 3. 编程接口使用

#### 基础使用
```python
import asyncio
from db_cleaner import create_cleaner

async def clean_database():
    cleaner = create_cleaner(
        database_type="mongodb",
        connection_params={
            "host": "localhost",
            "port": 27017,
            "database": "smartfilemanager"
        }
    )
    
    async with cleaner:
        # 清理指定集合
        report = await cleaner.clean_selective(
            targets=["files", "folders"],
            confirm=True
        )
        print(report.get_summary())

asyncio.run(clean_database())
```

#### 使用配置文件
```python
from db_cleaner import CleanerFactory

async def clean_with_config():
    cleaner = CleanerFactory.create_cleaner_from_file("config.yaml")
    
    async with cleaner:
        report = await cleaner.clean_all()
        print(report.get_summary())

asyncio.run(clean_with_config())
```

### 4. 运行示例

```bash
# 运行智能文件管理器清理示例
python smartfilemanager_cleaner_example.py
```

## 🔧 配置详解

### 数据库配置
```yaml
database:
  type: mongodb          # 数据库类型
  host: localhost        # 主机地址
  port: 27017           # 端口
  database: smartfilemanager  # 数据库名
  # username: user      # 用户名（可选）
  # password: pass      # 密码（可选）
```

### 清理规则
```yaml
rules:
  - target: files       # 目标集合
    conditions: {}      # 清理条件
    backup: true        # 是否备份
    operation: delete   # 操作类型
    batch_size: 1000   # 批处理大小
    timeout: 60        # 超时时间
```

### 安全设置
```yaml
safety:
  confirm_cleanup: true      # 需要确认
  dry_run: false            # 试运行模式
  max_delete_count: 10000   # 最大删除数量
```

## 🛡️ 安全建议

### 1. 备份策略
- 始终在清理前创建备份
- 定期验证备份文件的完整性
- 保留多个版本的备份

### 2. 测试流程
```bash
# 1. 先在测试环境验证
python -m db_cleaner.cli clean --config config.yaml --dry-run

# 2. 创建备份
python -m db_cleaner.cli backup --config config.yaml --output ./backup

# 3. 执行清理
python -m db_cleaner.cli clean --config config.yaml
```

### 3. 权限控制
- 使用专门的数据库用户
- 限制用户权限范围
- 记录所有操作日志

## 🐛 故障排除

### 常见问题

1. **连接失败**
```bash
# 检查数据库状态
systemctl status mongod

# 测试连接
python -c "from pymongo import MongoClient; print(MongoClient('localhost', 27017).admin.command('ping'))"
```

2. **权限错误**
```bash
# 检查数据库权限
mongo --eval "db.runCommand({connectionStatus: 1})"
```

3. **依赖缺失**
```bash
# 安装缺失的依赖
pip install motor pymongo
```

### 调试模式
```bash
# 启用详细日志
python -m db_cleaner.cli clean --config config.yaml --log-level DEBUG
```

## 📊 性能优化

### 1. 批处理大小调整
```yaml
rules:
  - target: large_collection
    batch_size: 5000  # 大集合使用更大的批处理
```

### 2. 并发控制
```yaml
options:
  parallel_operations: true  # 启用并行操作
```

### 3. 连接池优化
```yaml
database:
  options:
    maxPoolSize: 20  # 增加连接池大小
```

## 🔄 定期维护

### 1. 定时清理
```yaml
schedule:
  enabled: true
  cron: "0 2 * * 0"  # 每周日凌晨2点
```

### 2. 监控脚本
```bash
#!/bin/bash
# cleanup_monitor.sh

LOG_FILE="/var/log/db_cleaner.log"
ERROR_COUNT=$(grep -c "ERROR" "$LOG_FILE")

if [ "$ERROR_COUNT" -gt 0 ]; then
    echo "发现 $ERROR_COUNT 个错误，请检查日志"
    exit 1
fi

echo "清理任务正常完成"
```

### 3. 备份清理
```bash
# 清理旧备份（保留最近10个）
find ./backups -name "*.json" -type f -mtime +10 -delete
```

## 📞 获取帮助

- 查看命令帮助: `python -m db_cleaner.cli --help`
- 查看子命令帮助: `python -m db_cleaner.cli clean --help`
- 运行示例: `python smartfilemanager_cleaner_example.py`
- 查看配置示例: `cat smartfilemanager_cleaner_config.yaml`
