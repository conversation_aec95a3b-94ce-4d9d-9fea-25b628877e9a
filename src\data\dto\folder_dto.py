#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
文件夹数据传输对象

该模块定义了文件夹相关的DTO类，用于在不同层之间传输文件夹数据。
实现了以文件夹为核心的数据结构，支持层次化文件树管理。

作者: AI助手
日期: 2024-01-01
版本: 1.0.0
"""

import hashlib
import os
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List, Union
from src.utils.path_utils import _normalize_path
from src.utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class FolderDTO:
    """
    文件夹数据传输对象
    
    用于在不同层之间传输文件夹数据，支持序列化和反序列化。
    实现了以文件夹为核心的数据结构设计。
    """
    folder_id: str                                    # 文件夹唯一标识符
    path: str                                         # 文件夹完整路径（标准化）
    name: str                                         # 文件夹名称
    parent_id: Optional[str] = None                   # 父文件夹ID
    depth: int = 1                                    # 目录深度（根目录为1）
    child_folder_ids: List[str] = field(default_factory=list)  # 子文件夹ID列表
    files_hash: Optional[str] = None                  # 文件内容哈希值
    files_count: int = 0                              # 直接文件数量
    subfolders_count: int = 0                         # 子文件夹数量
    total_size: int = 0                               # 总大小（仅直接文件）
    created_time: Optional[datetime] = None           # 创建时间
    modified_time: Optional[datetime] = None          # 修改时间
    last_scan_time: Optional[datetime] = None         # 最后扫描时间
    scan_status: str = "pending"                      # 扫描状态：pending, scanning, completed, error
    is_network_folder: bool = False                   # 是否为网络文件夹
    metadata: Dict[str, Any] = field(default_factory=dict)  # 其他元数据
    
    def __post_init__(self):
        """初始化后处理"""
        # 标准化路径
        self.path = _normalize_path(self.path)
        
        # 如果没有提供name，从路径中提取
        if not self.name and self.path:
            self.name = os.path.basename(self.path) or self.path
        
        # 验证数据完整性
        self._validate()
    
    def _validate(self) -> None:
        """验证数据完整性"""
        if not self.folder_id:
            raise ValueError("folder_id不能为空")
        
        if not self.path:
            raise ValueError("path不能为空")
        
        if self.depth < 1:
            raise ValueError("depth必须大于等于1")
        
        if self.files_count < 0:
            raise ValueError("files_count不能为负数")
        
        if self.subfolders_count < 0:
            raise ValueError("subfolders_count不能为负数")
        
        if self.total_size < 0:
            raise ValueError("total_size不能为负数")
    
    @classmethod
    def from_path(cls, folder_path: str, parent_id: Optional[str] = None, 
                  folder_id: Optional[str] = None) -> 'FolderDTO':
        """
        从文件夹路径创建FolderDTO对象
        
        参数:
            folder_path: 文件夹路径
            parent_id: 父文件夹ID
            folder_id: 文件夹ID（如果不提供则自动生成）
        
        返回:
            FolderDTO对象
        """
        try:
            folder_path = _normalize_path(folder_path)
            path_obj = Path(folder_path)
            
            # 检查路径是否存在且为目录
            if not path_obj.exists():
                logger.warning(f"文件夹路径不存在: {folder_path}")
            elif not path_obj.is_dir():
                raise ValueError(f"路径不是文件夹: {folder_path}")
            
            # 生成文件夹ID
            if not folder_id:
                folder_id = cls._generate_folder_id(folder_path)
            
            # 计算深度
            depth = len(path_obj.parts) if parent_id is None else 1
            if parent_id:
                # 如果有父ID，需要查询父文件夹的深度（这里简化处理）
                depth = folder_path.count('/') + 1 if '/' in folder_path else folder_path.count('\\') + 1
            
            # 获取文件夹统计信息
            stats = cls._get_folder_stats(folder_path) if path_obj.exists() else {}
            
            return cls(
                folder_id=folder_id,
                path=folder_path,
                name=path_obj.name or folder_path,
                parent_id=parent_id,
                depth=depth,
                files_count=stats.get('files_count', 0),
                subfolders_count=stats.get('subfolders_count', 0),
                total_size=stats.get('total_size', 0),
                created_time=datetime.fromtimestamp(path_obj.stat().st_ctime) if path_obj.exists() else None,
                modified_time=datetime.fromtimestamp(path_obj.stat().st_mtime) if path_obj.exists() else None,
                scan_status="pending"
            )
            
        except Exception as e:
            logger.error(f"从路径创建FolderDTO失败 {folder_path}: {e}")
            raise
    
    @staticmethod
    def _generate_folder_id(folder_path: str) -> str:
        """
        生成文件夹唯一ID
        
        参数:
            folder_path: 文件夹路径
        
        返回:
            唯一ID字符串
        """
        # 使用路径的MD5哈希作为ID的一部分，确保唯一性
        path_hash = hashlib.md5(folder_path.encode('utf-8')).hexdigest()[:8]
        timestamp = int(datetime.now().timestamp() * 1000000)  # 微秒时间戳
        return f"folder_{path_hash}_{timestamp}"
    
    @staticmethod
    def _get_folder_stats(folder_path: str) -> Dict[str, int]:
        """
        获取文件夹统计信息
        
        参数:
            folder_path: 文件夹路径
        
        返回:
            统计信息字典
        """
        try:
            path_obj = Path(folder_path)
            if not path_obj.exists() or not path_obj.is_dir():
                return {'files_count': 0, 'subfolders_count': 0, 'total_size': 0}
            
            files_count = 0
            subfolders_count = 0
            total_size = 0
            
            for item in path_obj.iterdir():
                if item.is_file():
                    files_count += 1
                    try:
                        total_size += item.stat().st_size
                    except (OSError, PermissionError):
                        pass  # 忽略无法访问的文件
                elif item.is_dir():
                    subfolders_count += 1
            
            return {
                'files_count': files_count,
                'subfolders_count': subfolders_count,
                'total_size': total_size
            }
            
        except (OSError, PermissionError) as e:
            logger.warning(f"获取文件夹统计信息失败 {folder_path}: {e}")
            return {'files_count': 0, 'subfolders_count': 0, 'total_size': 0}

    def to_dict(self) -> Dict[str, Any]:
        """
        将FolderDTO转换为字典

        返回:
            字典格式的文件夹数据
        """
        return {
            "folder_id": self.folder_id,
            "path": self.path,
            "name": self.name,
            "parent_id": self.parent_id,
            "depth": self.depth,
            "child_folder_ids": self.child_folder_ids.copy(),
            "files_hash": self.files_hash,
            "files_count": self.files_count,
            "subfolders_count": self.subfolders_count,
            "total_size": self.total_size,
            "created_time": self.created_time.isoformat() if self.created_time else None,
            "modified_time": self.modified_time.isoformat() if self.modified_time else None,
            "last_scan_time": self.last_scan_time.isoformat() if self.last_scan_time else None,
            "scan_status": self.scan_status,
            "is_network_folder": self.is_network_folder,
            "metadata": self.metadata.copy()
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'FolderDTO':
        """
        从字典创建FolderDTO对象

        参数:
            data: 字典格式的文件夹数据

        返回:
            FolderDTO对象
        """
        try:
            # 处理时间字段
            created_time = None
            if data.get("created_time"):
                created_time = datetime.fromisoformat(data["created_time"])

            modified_time = None
            if data.get("modified_time"):
                modified_time = datetime.fromisoformat(data["modified_time"])

            last_scan_time = None
            if data.get("last_scan_time"):
                last_scan_time = datetime.fromisoformat(data["last_scan_time"])

            return cls(
                folder_id=data["folder_id"],
                path=data["path"],
                name=data["name"],
                parent_id=data.get("parent_id"),
                depth=data.get("depth", 1),
                child_folder_ids=data.get("child_folder_ids", []).copy(),
                files_hash=data.get("files_hash"),
                files_count=data.get("files_count", 0),
                subfolders_count=data.get("subfolders_count", 0),
                total_size=data.get("total_size", 0),
                created_time=created_time,
                modified_time=modified_time,
                last_scan_time=last_scan_time,
                scan_status=data.get("scan_status", "pending"),
                is_network_folder=data.get("is_network_folder", False),
                metadata=data.get("metadata", {}).copy()
            )

        except Exception as e:
            logger.error(f"从字典创建FolderDTO失败: {e}")
            raise

    def add_child_folder(self, child_folder_id: str) -> None:
        """
        添加子文件夹ID

        参数:
            child_folder_id: 子文件夹ID
        """
        if child_folder_id not in self.child_folder_ids:
            self.child_folder_ids.append(child_folder_id)
            self.subfolders_count = len(self.child_folder_ids)

    def remove_child_folder(self, child_folder_id: str) -> bool:
        """
        移除子文件夹ID

        参数:
            child_folder_id: 子文件夹ID

        返回:
            是否成功移除
        """
        try:
            self.child_folder_ids.remove(child_folder_id)
            self.subfolders_count = len(self.child_folder_ids)
            return True
        except ValueError:
            return False

    def update_scan_status(self, status: str, scan_time: Optional[datetime] = None) -> None:
        """
        更新扫描状态

        参数:
            status: 新的扫描状态
            scan_time: 扫描时间（默认为当前时间）
        """
        self.scan_status = status
        self.last_scan_time = scan_time or datetime.now()

    def is_root_folder(self) -> bool:
        """
        判断是否为根文件夹

        返回:
            是否为根文件夹
        """
        return self.parent_id is None or self.depth == 1

    def get_children_count(self) -> int:
        """
        获取子项总数（文件夹 + 文件）

        返回:
            子项总数
        """
        return self.subfolders_count + self.files_count

    def __str__(self) -> str:
        """字符串表示"""
        return f"FolderDTO(id={self.folder_id}, path={self.path}, children={self.get_children_count()})"

    def __repr__(self) -> str:
        """详细字符串表示"""
        return (f"FolderDTO(folder_id='{self.folder_id}', path='{self.path}', "
                f"parent_id='{self.parent_id}', depth={self.depth}, "
                f"files_count={self.files_count}, subfolders_count={self.subfolders_count})")
