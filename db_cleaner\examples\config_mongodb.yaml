# MongoDB数据库清理配置示例
# 适用于智能文件管理器项目

# 数据库连接配置
database:
  type: mongodb
  host: localhost
  port: 27017
  database: smartfilemanager
  # 可选：使用连接URI
  # uri: "************************************************************"
  
  # 连接选项
  options:
    serverSelectionTimeoutMS: 5000
    connectTimeoutMS: 5000

# 清理规则配置
rules:
  # 清理文件信息集合
  - target: files
    conditions: {}  # 清理所有文档，可以指定条件如 {"scan_status": "failed"}
    backup: true
    operation: delete
    batch_size: 1000
    timeout: 60
  
  # 清理文件夹信息集合
  - target: folders
    conditions: {}
    backup: true
    operation: delete
    batch_size: 500
    timeout: 60
  
  # 清理临时数据（示例：只清理特定条件的数据）
  - target: temp_data
    conditions:
      created_time:
        $lt: "2024-01-01T00:00:00Z"  # 清理2024年之前的临时数据
    backup: false
    operation: delete
    batch_size: 2000
    timeout: 30

# 备份配置
backup:
  enabled: true
  directory: "./backups"
  format: json  # json, bson, csv
  compress: true
  keep_count: 5  # 保留最近5个备份

# 定时任务配置
schedule:
  enabled: false
  # cron表达式：每天凌晨2点执行
  cron: "0 2 * * *"
  # 或者使用间隔时间（秒）
  # interval: 86400  # 24小时

# 日志配置
logging:
  level: INFO
  file: "./logs/db_cleaner.log"
  color: true
  format: "%(asctime)s [%(levelname)s] [%(name)s] %(message)s"

# 安全设置
safety:
  confirm_cleanup: true      # 是否需要确认清理操作
  dry_run: false            # 试运行模式
  protected_collections: [] # 受保护的集合，不会被清理
  max_delete_count: 10000   # 单次最大删除数量

# 高级选项
options:
  parallel_operations: false  # 是否并行执行操作
  progress_reporting: true    # 是否报告进度
  auto_optimize: true         # 清理后是否自动优化数据库
