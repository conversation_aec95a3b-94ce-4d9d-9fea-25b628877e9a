#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库清理工具日志系统

提供统一的日志记录功能，支持多种输出格式和级别。

作者: AI助手
日期: 2024-01-01
版本: 1.0.0
"""

import logging
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any


class ColoredFormatter(logging.Formatter):
    """带颜色的日志格式化器"""
    
    # ANSI颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def format(self, record):
        # 添加颜色
        if record.levelname in self.COLORS:
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)


class CleanerLogger:
    """数据库清理工具专用日志器"""
    
    def __init__(self, name: str = "db_cleaner", level: str = "INFO", 
                 log_file: Optional[str] = None, enable_color: bool = True):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, level.upper()))
        
        # 清除现有处理器
        self.logger.handlers.clear()
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(getattr(logging, level.upper()))
        
        # 格式化器
        if enable_color and sys.stdout.isatty():
            console_formatter = ColoredFormatter(
                '%(asctime)s [%(levelname)s] [%(name)s] %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
        else:
            console_formatter = logging.Formatter(
                '%(asctime)s [%(levelname)s] [%(name)s] %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
        
        console_handler.setFormatter(console_formatter)
        self.logger.addHandler(console_handler)
        
        # 文件处理器
        if log_file:
            self._setup_file_handler(log_file, level)
    
    def _setup_file_handler(self, log_file: str, level: str):
        """设置文件日志处理器"""
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(getattr(logging, level.upper()))
        
        file_formatter = logging.Formatter(
            '%(asctime)s [%(levelname)s] [%(name)s] [%(filename)s:%(lineno)d] %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        file_handler.setFormatter(file_formatter)
        self.logger.addHandler(file_handler)
    
    def debug(self, message: str, **kwargs):
        """记录调试信息"""
        self.logger.debug(message, **kwargs)
    
    def info(self, message: str, **kwargs):
        """记录一般信息"""
        self.logger.info(message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """记录警告信息"""
        self.logger.warning(message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """记录错误信息"""
        self.logger.error(message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        """记录严重错误信息"""
        self.logger.critical(message, **kwargs)
    
    def log_operation(self, operation: str, target: str, result: str, 
                     duration: float = None, details: Dict[str, Any] = None):
        """记录操作日志"""
        message = f"操作: {operation}, 目标: {target}, 结果: {result}"
        
        if duration is not None:
            message += f", 耗时: {duration:.3f}秒"
        
        if details:
            detail_str = ", ".join([f"{k}: {v}" for k, v in details.items()])
            message += f", 详情: {detail_str}"
        
        self.info(message)
    
    def log_cleanup_start(self, targets: list, config: Dict[str, Any] = None):
        """记录清理开始"""
        self.info(f"开始数据库清理任务")
        self.info(f"清理目标: {targets}")
        if config:
            self.info(f"清理配置: {config}")
    
    def log_cleanup_end(self, success: bool, summary: Dict[str, Any] = None):
        """记录清理结束"""
        status = "成功" if success else "失败"
        self.info(f"数据库清理任务{status}")
        
        if summary:
            for key, value in summary.items():
                self.info(f"  {key}: {value}")


# 全局日志器实例
_global_logger: Optional[CleanerLogger] = None


def get_logger(name: str = "db_cleaner", level: str = "INFO", 
               log_file: Optional[str] = None, enable_color: bool = True) -> CleanerLogger:
    """
    获取日志器实例
    
    参数:
        name: 日志器名称
        level: 日志级别
        log_file: 日志文件路径
        enable_color: 是否启用颜色
    
    返回:
        CleanerLogger实例
    """
    global _global_logger
    
    if _global_logger is None:
        _global_logger = CleanerLogger(name, level, log_file, enable_color)
    
    return _global_logger


def setup_logging(config: Dict[str, Any]) -> CleanerLogger:
    """
    根据配置设置日志系统
    
    参数:
        config: 日志配置字典
    
    返回:
        配置好的日志器
    """
    return get_logger(
        name=config.get('name', 'db_cleaner'),
        level=config.get('level', 'INFO'),
        log_file=config.get('file'),
        enable_color=config.get('color', True)
    )
