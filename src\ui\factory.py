#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
UI工厂实现模块

该模块提供UI组件的工厂实现：
1. 标准UI工厂
2. 组件创建和配置
3. 依赖注入集成
4. 主题管理

作者: AI助手
日期: 2023-06-01
版本: 1.0.0
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, Any, Optional
import os

from .interfaces import (
    IUIFactory, IMainWindow, ITreePanel, IPanel, IStatusDisplay
)
# 移除循环导入
# from src.core.dependency_injection import resolve
from src.utils.event_system import EventSystem
from src.utils.async_manager import AsyncManager
from src.utils.logger import get_logger


class UIThemeManager:
    """UI主题管理器"""
    
    def __init__(self):
        self.logger = get_logger("UIThemeManager")
        self._current_theme = self._get_default_theme()
    
    def _get_default_theme(self) -> Dict[str, Any]:
        """获取默认主题"""
        return {
            "colors": {
                "primary": "#4a7a8c",
                "secondary": "#e1e1e1",
                "background": "#f0f0f0",
                "accent": "#6b6b6b",
                "success": "#28a745",
                "warning": "#ffc107",
                "error": "#dc3545",
                "info": "#17a2b8"
            },
            "fonts": {
                "default": ("Helvetica", 10),
                "heading": ("Helvetica", 12, "bold"),
                "small": ("Helvetica", 8)
            },
            "font_size": 10,  # 新增：全局字体大小
            "spacing": {
                "small": 5,
                "medium": 10,
                "large": 15
            },
            "sizes": {
                "button_padding": 6,
                "frame_padding": 10,
                "scrollbar_width": 12,
                "progressbar_thickness": 15
            }
        }
    
    def apply_theme_to_style(self, style: ttk.Style) -> None:
        """将主题应用到样式"""
        theme = self._current_theme
        colors = theme["colors"]
        fonts = theme["fonts"]
        sizes = theme["sizes"]
        
        # 配置基础样式
        style.configure("TFrame", background=colors["background"])
        style.configure("TButton", 
                       padding=sizes["button_padding"], 
                       relief="flat", 
                       background=colors["primary"],
                       font=fonts["default"])
        style.configure("TLabel", 
                       background=colors["background"],
                       font=fonts["default"])
        style.configure("TNotebook", background=colors["background"])
        style.configure("TNotebook.Tab", 
                       padding=[10, 5], 
                       font=fonts["default"])
        
        # 配置进度条样式
        style.configure("TProgressbar",
                       thickness=sizes["progressbar_thickness"],
                       borderwidth=0,
                       background=colors["primary"],
                       troughcolor=colors["secondary"],
                       lightcolor=colors["primary"],
                       darkcolor=colors["primary"])
        
        # 配置滚动条样式
        scrollbar_style = {
            "gripcount": 0,
            "background": colors["primary"],
            "darkcolor": colors["accent"],
            "lightcolor": colors["accent"],
            "troughcolor": colors["secondary"],
            "arrowsize": 14,
            "width": sizes["scrollbar_width"]
        }
        
        style.configure("Custom.Vertical.TScrollbar", **scrollbar_style)
        style.configure("Custom.Horizontal.TScrollbar", **scrollbar_style)
    
    def get_theme(self) -> Dict[str, Any]:
        """获取当前主题"""
        return self._current_theme.copy()
    
    def set_theme(self, theme: Dict[str, Any]) -> None:
        """设置主题"""
        self._current_theme = theme
        self.logger.info("主题已更新")
    
    def get_font_size(self) -> int:
        """获取当前字体大小"""
        return self._current_theme.get("font_size", 10)
    
    def set_font_size(self, font_size: int) -> None:
        """设置字体大小"""
        self._current_theme["font_size"] = font_size
        # 更新字体配置
        self._current_theme["fonts"]["default"] = ("Helvetica", font_size)
        self._current_theme["fonts"]["heading"] = ("Helvetica", font_size + 2, "bold")
        self._current_theme["fonts"]["small"] = ("Helvetica", max(font_size - 2, 6))
        self.logger.info(f"字体大小已更新为: {font_size}")
    
    def apply_font_size_to_style(self, style: ttk.Style, font_size: int) -> None:
        """将字体大小应用到样式"""
        self.set_font_size(font_size)
        
        # 配置基础样式
        style.configure("TFrame", background=self._current_theme["colors"]["background"])
        style.configure("TButton", 
                       padding=self._current_theme["sizes"]["button_padding"], 
                       relief="flat", 
                       background=self._current_theme["colors"]["primary"],
                       font=("Helvetica", font_size))
        style.configure("TLabel", 
                       background=self._current_theme["colors"]["background"],
                       font=("Helvetica", font_size))
        style.configure("TNotebook", background=self._current_theme["colors"]["background"])
        style.configure("TNotebook.Tab", 
                       padding=[10, 5], 
                       font=("Helvetica", font_size))
        
        # 配置进度条样式
        style.configure("TProgressbar",
                       thickness=self._current_theme["sizes"]["progressbar_thickness"],
                       borderwidth=0,
                       background=self._current_theme["colors"]["primary"],
                       troughcolor=self._current_theme["colors"]["secondary"],
                       lightcolor=self._current_theme["colors"]["primary"],
                       darkcolor=self._current_theme["colors"]["primary"])
        
        # 配置滚动条样式
        scrollbar_style = {
            "gripcount": 0,
            "background": self._current_theme["colors"]["primary"],
            "darkcolor": self._current_theme["colors"]["accent"],
            "lightcolor": self._current_theme["colors"]["accent"],
            "troughcolor": self._current_theme["colors"]["secondary"],
            "arrowsize": 14,
            "width": self._current_theme["sizes"]["scrollbar_width"]
        }
        
        style.configure("Custom.Vertical.TScrollbar", **scrollbar_style)
        style.configure("Custom.Horizontal.TScrollbar", **scrollbar_style)
        
        # 配置树形视图样式
        style.configure("Treeview", 
                       rowheight=max(25, font_size + 15),
                       font=("Helvetica", font_size))
        style.configure("Treeview.Heading", 
                       font=("Helvetica", font_size, "bold"))
        
        # 配置列表框样式
        style.configure("TListbox", 
                       font=("Helvetica", font_size))
        
        # 配置输入框样式
        style.configure("TEntry", 
                       font=("Helvetica", font_size))
        
        # 配置组合框样式
        style.configure("TCombobox", 
                       font=("Helvetica", font_size))
        
        # 配置文本样式
        style.configure("TText", 
                       font=("Helvetica", font_size))
        
        style.configure("TCheckbutton", font=("Helvetica", font_size))
        style.configure("TLabelframe.Label", font=("Helvetica", font_size))
        style.configure("TLabelframe", font=("Helvetica", font_size))
        style.configure("TMenubutton", font=("Helvetica", font_size))

        self.logger.info(f"字体大小 {font_size} 已应用到所有样式")
    
    def get_available_themes(self) -> Dict[str, Dict[str, Any]]:
        """获取可用主题列表"""
        return {
            "default": self._get_default_theme(),
            "dark": self._get_dark_theme(),
            "light": self._get_light_theme()
        }
    
    def _get_dark_theme(self) -> Dict[str, Any]:
        """获取深色主题"""
        return {
            "colors": {
                "primary": "#2d3748",
                "secondary": "#4a5568",
                "background": "#1a202c",
                "accent": "#718096",
                "success": "#38a169",
                "warning": "#d69e2e",
                "error": "#e53e3e",
                "info": "#3182ce"
            },
            "fonts": {
                "default": ("Helvetica", 10),
                "heading": ("Helvetica", 12, "bold"),
                "small": ("Helvetica", 8)
            },
            "spacing": {
                "small": 5,
                "medium": 10,
                "large": 15
            },
            "sizes": {
                "button_padding": 6,
                "frame_padding": 10,
                "scrollbar_width": 12,
                "progressbar_thickness": 15
            }
        }
    
    def _get_light_theme(self) -> Dict[str, Any]:
        """获取浅色主题"""
        return {
            "colors": {
                "primary": "#3182ce",
                "secondary": "#e2e8f0",
                "background": "#ffffff",
                "accent": "#a0aec0",
                "success": "#38a169",
                "warning": "#d69e2e",
                "error": "#e53e3e",
                "info": "#3182ce"
            },
            "fonts": {
                "default": ("Helvetica", 10),
                "heading": ("Helvetica", 12, "bold"),
                "small": ("Helvetica", 8)
            },
            "spacing": {
                "small": 5,
                "medium": 10,
                "large": 15
            },
            "sizes": {
                "button_padding": 6,
                "frame_padding": 10,
                "scrollbar_width": 12,
                "progressbar_thickness": 15
            }
        }


class StandardUIFactory(IUIFactory):
    """标准UI组件工厂"""
    
    def __init__(self):
        # from src.core.dependency_injection import configure_core_services
        # configure_core_services()
        self.logger = get_logger("StandardUIFactory")
        self.theme_manager = UIThemeManager()
        self._event_system: Optional[EventSystem] = None
        self.root: tk.Tk | None = None

    def set_root_window(self, root: tk.Tk):
        '''设置根窗口引用'''
        self.root = root
    
    def _get_event_system(self):
        """获取事件系统"""
        if not hasattr(self, '_event_system'):
            # self._event_system = resolve(EventSystem)
            self._event_system = EventSystem()  # 直接实例化
        return self._event_system
    
    def _get_event_system_alt(self):
        """获取事件系统（备用方法）"""
        # self._event_system = resolve(EventSystem)
        self._event_system = EventSystem()  # 直接实例化
        return self._event_system
    
    def _setup_dependencies(self):
        """设置依赖项"""
        style = ttk.Style()
        style.theme_use("clam")
        self.theme_manager.apply_theme_to_style(style)
        # 移除循环导入
        # from src.core.dependency_injection import resolve
        # 全部依赖注入
        # event_system = resolve(EventSystem)
        event_system = EventSystem()  # 直接实例化
        # async_manager = resolve(AsyncManager)
        async_manager = AsyncManager()  # 直接实例化
        # logger = resolve(get_logger("SmartFileManagerApp", task_type="system").__class__)
        logger = get_logger("SmartFileManagerApp")  # 直接实例化
        from src.utils.config_loader import ConfigLoader
        from src.data.db_manager import MongoDBManager
        from src.core.rule_engine import RuleEngine
        from src.core.file_scanner import FileScanner
        from src.video_analyzer import VideoAnalyzer
        from src.core.file_operations import FileOperations
        
        # config_loader = resolve(ConfigLoader)
        config_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'config')
        config_loader = ConfigLoader(config_dir)  # 传递config_dir参数
        # db_manager = resolve(MongoDBManager)
        db_manager = MongoDBManager()  # 直接实例化
        # rule_engine = resolve(RuleEngine)
        rule_engine = RuleEngine()  # 直接实例化
        # file_scanner = resolve(FileScanner)
        from src.utils.async_task_manager import AsyncTaskManager
        async_task_manager = AsyncTaskManager()
        file_scanner = FileScanner(db_manager=db_manager, async_task_manager=async_task_manager)  # 提供必需参数
        # video_analyzer = resolve(VideoAnalyzer)
        video_analyzer = VideoAnalyzer()  # 直接实例化
        # file_operations = resolve(FileOperations)
        file_operations = FileOperations()  # 直接实例化
    
    def create_main_window(self, root: tk.Tk) -> IMainWindow:
        """创建主窗口，所有依赖通过容器resolve注入"""
        self.logger.info("创建主窗口")
        from .main_window import MainWindow
        style = ttk.Style()
        style.theme_use("clam")
        self.theme_manager.apply_theme_to_style(style)
        # 移除循环导入
        # from src.core.dependency_injection import resolve
        # 全部依赖注入
        # event_system = resolve(EventSystem)
        event_system = EventSystem()  # 直接实例化
        # async_manager = resolve(AsyncManager)
        async_manager = AsyncManager()  # 直接实例化
        # logger = resolve(get_logger("SmartFileManagerApp", task_type="system").__class__)
        logger = get_logger("SmartFileManagerApp")  # 直接实例化
        from src.utils.config_loader import ConfigLoader
        from src.data.db_manager import MongoDBManager
        from src.core.rule_engine import RuleEngine
        from src.core.file_scanner import FileScanner
        from src.video_analyzer import VideoAnalyzer
        from src.core.file_operations import FileOperations
        config_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'config')
        config_loader = ConfigLoader(config_dir)
        db_manager = MongoDBManager()
        rule_engine = RuleEngine()
        from src.utils.async_task_manager import AsyncTaskManager
        async_task_manager = AsyncTaskManager()
        file_scanner = FileScanner(db_manager=db_manager, async_task_manager=async_task_manager)
        video_analyzer = VideoAnalyzer()
        file_operations = FileOperations()
        main_window = MainWindow(root, self, event_system, async_manager, logger, config_loader, db_manager, rule_engine, file_scanner, video_analyzer, file_operations)
        if not hasattr(main_window, 'update_interval'):
            main_window.update_interval = 100
            self.logger.debug(f"[Factory] 设置默认update_interval: {main_window.update_interval}")
        main_window.apply_theme(self.theme_manager.get_theme())
        return main_window
    
    def create_file_tree_panel(self, parent: tk.Widget, main_window: Any) -> Any:
        """创建文件树面板，依赖通过容器注入"""
        try:
            self.logger.debug("创建文件树面板")
            if self.root is None:
                raise ValueError("必须先调用set_root_window()设置根窗口")
            from .file_tree import FileTreePanel
            # panel = FileTreePanel(parent, main_window, resolve(Logger))
            panel = FileTreePanel(parent, main_window, get_logger("FileTreePanel"))
            self._configure_panel(panel)
            if panel is None or not hasattr(panel, 'get_frame'):
                raise ValueError("文件树面板创建失败")
            return panel
        except Exception as e:
            self.logger.error(f"创建文件树面板失败: {e}")
            raise
    
    def create_rename_panel(self, parent: tk.Widget, main_window: Any) -> Any:
        """创建重命名面板，依赖通过容器注入"""
        try:
            if self.root is None:
                raise ValueError("必须先调用set_root_window()设置根窗口")
            self.logger.debug("创建重命名面板")
            from .rename_panel import RenamePanel
            # panel = RenamePanel(parent, main_window, resolve(Logger))
            panel = RenamePanel(parent, main_window, get_logger("RenamePanel"))
            self._configure_panel(panel)
            if panel is None or not hasattr(panel, 'get_frame'):
                raise ValueError("重命名面板创建失败")
            return panel
        except Exception as e:
            self.logger.error(f"创建重命名面板失败: {e}")
            raise
    
    def create_duplicate_panel(self, parent: tk.Widget, main_window: Any) -> Any:
        """创建重复文件面板，依赖通过容器注入"""
        try:
            if self.root is None:
                raise ValueError("必须先调用set_root_window()设置根窗口")
            self.logger.debug("创建重复文件面板")
            from .duplicate_panel import DuplicatePanel
            # panel = DuplicatePanel(parent, main_window, resolve(Logger))
            panel = DuplicatePanel(parent, main_window, get_logger("DuplicatePanel"))
            self._configure_panel(panel)
            if panel is None or not hasattr(panel, 'get_frame'):
                raise ValueError("重复文件面板创建失败")
            return panel
        except Exception as e:
            self.logger.error(f"创建重复文件面板失败: {e}")
            raise
    
    def create_junk_panel(self, parent: tk.Widget, main_window: Any) -> Any:
        """创建垃圾文件面板，依赖通过容器注入"""
        try:
            if self.root is None:
                raise ValueError("必须先调用set_root_window()设置根窗口")
            self.logger.debug("创建垃圾文件面板")
            from .junk_panel import JunkPanel
            # panel = JunkPanel(parent, main_window, resolve(Logger))
            panel = JunkPanel(parent, main_window, get_logger("JunkPanel"))
            self._configure_panel(panel)
            if panel is None or not hasattr(panel, 'get_frame'):
                raise ValueError("垃圾文件面板创建失败")
            return panel
        except Exception as e:
            self.logger.error(f"创建垃圾文件面板失败: {e}")
            raise
    
    def create_whitelist_panel(self, parent: tk.Widget, main_window: Any) -> Any:
        """创建白名单面板，依赖通过容器注入"""
        try:
            if self.root is None:
                raise ValueError("必须先调用set_root_window()设置根窗口")
            self.logger.debug("创建白名单面板")
            from .whitelist_panel import WhitelistPanel
            # panel = WhitelistPanel(parent, main_window, resolve(Logger))
            panel = WhitelistPanel(parent, main_window, get_logger("WhitelistPanel"))
            self._configure_panel(panel)
            if panel is None or not hasattr(panel, 'get_frame'):
                raise ValueError("白名单面板创建失败")
            return panel
        except Exception as e:
            self.logger.error(f"创建白名单面板失败: {e}")
            raise
    
    def create_settings_panel(self, parent: tk.Widget, main_window: Any) -> Any:
        """创建设置面板，依赖通过容器注入"""
        try:
            if self.root is None:
                raise ValueError("必须先调用set_root_window()设置根窗口")
            self.logger.debug("创建设置面板")
            from .settings_panel import SettingsPanel
            # panel = SettingsPanel(parent, main_window, resolve(Logger))
            panel = SettingsPanel(parent, main_window, get_logger("SettingsPanel"))
            self._configure_panel(panel)
            if panel is None or not hasattr(panel, 'get_frame'):
                raise ValueError("设置面板创建失败")
            return panel
        except Exception as e:
            self.logger.error(f"创建设置面板失败: {e}")
            raise
    
    def create_style_panel(self, parent: tk.Widget, main_window: Any) -> Any:
        """创建风格统计面板，依赖通过容器注入"""
        try:
            if self.root is None:
                raise ValueError("必须先调用set_root_window()设置根窗口")
            self.logger.debug("创建风格统计面板")
            from .style_panel import StylePanel
            # panel = StylePanel(parent, main_window, resolve(Logger))
            panel = StylePanel(parent, main_window, get_logger("StylePanel"))
            self._configure_panel(panel)
            if panel is None or not hasattr(panel, 'get_frame'):
                raise ValueError("风格统计面板创建失败")
            return panel
        except Exception as e:
            self.logger.error(f"创建风格统计面板失败: {e}")
            raise
    
    def create_status_bar(self, parent: tk.Widget) -> Any:
        """创建状态栏"""
        try:
            self.logger.debug("创建状态栏")
            from .status_bar import StatusBar
            status_bar = StatusBar(parent)
            
            # 验证状态栏是否创建成功
            if status_bar is None or not hasattr(status_bar, 'show_message'):
                raise ValueError("状态栏创建失败")
                
            return status_bar
        except Exception as e:
            self.logger.error(f"创建状态栏失败: {e}")
            raise
    
    def _configure_panel(self, panel) -> None:
        """配置面板通用设置"""
        # 应用主题
        if hasattr(panel, 'apply_theme'):
            panel.apply_theme(self.theme_manager.get_theme())
        
        # 设置事件系统
        if hasattr(panel, 'set_event_system'):
            panel.set_event_system(self._get_event_system())
    
    def get_theme_manager(self):
        """获取主题管理器（可选）"""
        return self.theme_manager
    
    def apply_font_size(self, font_size: int) -> None:
        """应用字体大小到整个应用程序，并递归刷新所有控件"""
        try:
            if self.root:
                style = ttk.Style(self.root)
                self.theme_manager.apply_font_size_to_style(style, font_size)
                self.logger.info(f"字体大小 {font_size} 已应用到应用程序")
                # 递归刷新所有控件字体
                self._update_widget_fonts(self.root, font_size)
            else:
                self.logger.warning("根窗口未设置，无法应用字体大小")
        except Exception as e:
            self.logger.error(f"应用字体大小失败: {e}")

    def _update_widget_fonts(self, widget, font_size: int):
        """递归更新所有控件的字体，包括自定义控件，支持Frame嵌套和dict/list结构"""
        import tkinter.font as tkfont
        try:
            default_font = tkfont.Font(family="Helvetica", size=font_size)
            # 优先处理自定义控件
            if hasattr(widget, 'set_font') and callable(getattr(widget, 'set_font')):
                widget.set_font(default_font)
                return
            # 只对 tk 组件直接设置 font，ttk 组件跳过（ttk 由 style 控制）
            if isinstance(widget, (tk.Label, tk.Button, tk.Entry, tk.Text)):
                try:
                    widget.configure(font=default_font)
                except Exception:
                    pass
            # 递归Frame的子控件
            if isinstance(widget, (tk.Frame,)):
                for child in widget.winfo_children():
                    self._update_widget_fonts(child, font_size)
            # 递归dict/list结构（如StatusPanel.status_widgets）
            if isinstance(widget, dict):
                for v in widget.values():
                    self._update_widget_fonts(v, font_size)
            if isinstance(widget, list):
                for v in widget:
                    self._update_widget_fonts(v, font_size)
        except Exception as e:
            self.logger.warning(f"刷新控件字体失败: {e}")
    
    def get_current_font_size(self) -> int:
        """获取当前字体大小"""
        return self.theme_manager.get_font_size()

    def apply_font_to_widget(self, widget, font):
        """递归为控件及其所有子控件设置字体，优先调用自定义控件的set_font方法"""
        if hasattr(widget, 'set_font'):
            widget.set_font(font)
        else:
            try:
                widget.config(font=font)
            except Exception:
                pass
        for child in getattr(widget, 'winfo_children', lambda: [])():
            self.apply_font_to_widget(child, font)


# 全局工厂实例
_ui_factory: Optional[IUIFactory] = None


def get_ui_factory() -> IUIFactory:
    """获取UI工厂实例"""
    global _ui_factory
    if _ui_factory is None:
        _ui_factory = StandardUIFactory()
    return _ui_factory


def set_ui_factory(factory: IUIFactory) -> None:
    """设置UI工厂实例"""
    global _ui_factory
    _ui_factory = factory