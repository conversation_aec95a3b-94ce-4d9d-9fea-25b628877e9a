# MySQL数据库清理配置示例

# 数据库连接配置
database:
  type: mysql
  host: localhost
  port: 3306
  username: root
  password: your_password
  database: your_database
  
  # 连接选项
  options:
    charset: utf8mb4
    autocommit: true

# 清理规则配置
rules:
  # 清理用户日志表
  - target: user_logs
    conditions:
      created_at: 
        operator: "<"
        value: "2024-01-01 00:00:00"
    backup: true
    operation: delete
    batch_size: 1000
    timeout: 60
  
  # 清理临时表
  - target: temp_table
    conditions: {}
    backup: false
    operation: truncate
    timeout: 30

# 备份配置
backup:
  enabled: true
  directory: "./backups"
  format: json
  compress: true
  keep_count: 3

# 日志配置
logging:
  level: INFO
  file: "./logs/mysql_cleaner.log"
  color: true

# 安全设置
safety:
  confirm_cleanup: true
  dry_run: false
  protected_collections: ["users", "products"]
  max_delete_count: 5000
