#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
PostgreSQL数据库适配器

实现了PostgreSQL数据库的清理操作。

作者: AI助手
日期: 2024-01-01
版本: 1.0.0
"""

import json
import csv
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path

try:
    import asyncpg
    POSTGRESQL_AVAILABLE = True
except ImportError:
    POSTGRESQL_AVAILABLE = False

from .base import DatabaseAdapter
from ..utils.exceptions import DatabaseConnectionError, CleanupError, ConfigurationError


class PostgreSQLAdapter(DatabaseAdapter):
    """PostgreSQL数据库适配器"""
    
    def __init__(self, connection_params: Dict[str, Any]):
        """初始化PostgreSQL适配器"""
        if not POSTGRESQL_AVAILABLE:
            raise ConfigurationError("PostgreSQL依赖未安装，请运行: pip install asyncpg")
        
        super().__init__(connection_params)
        self.pool = None
        self.database_name = connection_params.get('database', 'postgres')
    
    async def connect(self) -> bool:
        """连接到PostgreSQL"""
        try:
            # 创建连接池
            self.pool = await asyncpg.create_pool(
                host=self.connection_params.get('host', 'localhost'),
                port=self.connection_params.get('port', 5432),
                user=self.connection_params.get('username', 'postgres'),
                password=self.connection_params.get('password', ''),
                database=self.database_name,
                min_size=1,
                max_size=10,
                command_timeout=60
            )
            
            # 测试连接
            async with self.pool.acquire() as conn:
                await conn.execute("SELECT 1")
            
            self.is_connected = True
            return True
            
        except Exception as e:
            raise DatabaseConnectionError(
                f"连接PostgreSQL失败: {e}",
                database_type="postgresql",
                connection_params=self.connection_params
            )
    
    async def disconnect(self) -> None:
        """断开PostgreSQL连接"""
        if self.pool:
            await self.pool.close()
        
        self.is_connected = False
    
    async def test_connection(self) -> bool:
        """测试PostgreSQL连接"""
        try:
            if not self.is_connected or not self.pool:
                return False
            
            async with self.pool.acquire() as conn:
                await conn.execute("SELECT 1")
                return True
                
        except Exception:
            return False
    
    async def get_database_info(self) -> Dict[str, Any]:
        """获取PostgreSQL数据库信息"""
        self._validate_connection()
        
        try:
            async with self.pool.acquire() as conn:
                # 获取版本信息
                version = await conn.fetchval("SELECT version()")
                
                # 获取数据库大小
                size_bytes = await conn.fetchval("""
                    SELECT pg_database_size($1)
                """, self.database_name)
                
                # 获取表数量
                table_count = await conn.fetchval("""
                    SELECT COUNT(*) 
                    FROM information_schema.tables 
                    WHERE table_schema = 'public'
                """)
                
                return {
                    "database_type": "postgresql",
                    "server_version": version,
                    "database_name": self.database_name,
                    "tables_count": table_count,
                    "size_bytes": size_bytes,
                    "size_mb": round(size_bytes / 1024 / 1024, 2) if size_bytes else 0
                }
                
        except Exception as e:
            raise CleanupError(f"获取数据库信息失败: {e}")
    
    async def list_collections(self) -> List[str]:
        """列出所有表"""
        self._validate_connection()
        
        try:
            async with self.pool.acquire() as conn:
                tables = await conn.fetch("""
                    SELECT table_name 
                    FROM information_schema.tables 
                    WHERE table_schema = 'public'
                """)
                
                return [table['table_name'] for table in tables]
                
        except Exception as e:
            raise CleanupError(f"列出表失败: {e}")
    
    async def get_collection_info(self, collection_name: str) -> Dict[str, Any]:
        """获取表信息"""
        self._validate_connection()
        
        try:
            async with self.pool.acquire() as conn:
                # 获取表统计信息
                stats = await conn.fetchrow("""
                    SELECT 
                        schemaname,
                        tablename,
                        attname,
                        n_distinct,
                        correlation
                    FROM pg_stats 
                    WHERE tablename = $1
                    LIMIT 1
                """, collection_name)
                
                # 获取表大小
                size_info = await conn.fetchrow("""
                    SELECT 
                        pg_total_relation_size($1) as total_size,
                        pg_relation_size($1) as table_size,
                        pg_indexes_size($1) as index_size
                """, collection_name)
                
                # 获取行数估计
                row_count = await conn.fetchval("""
                    SELECT reltuples::bigint 
                    FROM pg_class 
                    WHERE relname = $1
                """, collection_name)
                
                return {
                    "name": collection_name,
                    "count": int(row_count) if row_count else 0,
                    "total_size": size_info['total_size'] if size_info else 0,
                    "table_size": size_info['table_size'] if size_info else 0,
                    "index_size": size_info['index_size'] if size_info else 0
                }
                
        except Exception as e:
            raise CleanupError(
                f"获取表信息失败: {e}",
                operation="get_collection_info",
                target=collection_name
            )
    
    async def count_documents(self, collection_name: str, 
                            conditions: Dict[str, Any] = None) -> int:
        """统计记录数量"""
        self._validate_connection()
        
        try:
            async with self.pool.acquire() as conn:
                if conditions:
                    # 构建WHERE子句
                    where_clause, params = self._build_where_clause(conditions)
                    sql = f'SELECT COUNT(*) FROM "{collection_name}" WHERE {where_clause}'
                    count = await conn.fetchval(sql, *params)
                else:
                    sql = f'SELECT COUNT(*) FROM "{collection_name}"'
                    count = await conn.fetchval(sql)
                
                return count if count else 0
                
        except Exception as e:
            raise CleanupError(
                f"统计记录数量失败: {e}",
                operation="count_documents",
                target=collection_name
            )
    
    async def delete_documents(self, collection_name: str, 
                             conditions: Dict[str, Any] = None,
                             batch_size: int = 1000) -> int:
        """删除记录"""
        self._validate_connection()
        
        try:
            async with self.pool.acquire() as conn:
                if conditions is None:
                    # 删除所有记录
                    sql = f'DELETE FROM "{collection_name}"'
                    result = await conn.execute(sql)
                    # 从结果字符串中提取删除数量
                    return int(result.split()[-1]) if result.split()[-1].isdigit() else 0
                else:
                    # 按条件批量删除
                    where_clause, params = self._build_where_clause(conditions)
                    
                    total_deleted = 0
                    while True:
                        sql = f'''
                            DELETE FROM "{collection_name}" 
                            WHERE ctid IN (
                                SELECT ctid FROM "{collection_name}" 
                                WHERE {where_clause} 
                                LIMIT {batch_size}
                            )
                        '''
                        result = await conn.execute(sql, *params)
                        deleted_count = int(result.split()[-1]) if result.split()[-1].isdigit() else 0
                        total_deleted += deleted_count
                        
                        if deleted_count < batch_size:
                            break
                    
                    return total_deleted
                    
        except Exception as e:
            raise CleanupError(
                f"删除记录失败: {e}",
                operation="delete_documents",
                target=collection_name
            )
    
    async def truncate_collection(self, collection_name: str) -> bool:
        """清空表"""
        self._validate_connection()
        
        try:
            async with self.pool.acquire() as conn:
                sql = f'TRUNCATE TABLE "{collection_name}" RESTART IDENTITY CASCADE'
                await conn.execute(sql)
                return True
                
        except Exception as e:
            raise CleanupError(
                f"清空表失败: {e}",
                operation="truncate_collection",
                target=collection_name
            )
    
    async def backup_collection(self, collection_name: str, 
                               backup_path: str,
                               conditions: Dict[str, Any] = None,
                               format: str = "json") -> str:
        """备份表数据"""
        self._validate_connection()
        
        try:
            backup_file = Path(backup_path)
            backup_file.parent.mkdir(parents=True, exist_ok=True)
            
            async with self.pool.acquire() as conn:
                # 构建查询SQL
                if conditions:
                    where_clause, params = self._build_where_clause(conditions)
                    sql = f'SELECT * FROM "{collection_name}" WHERE {where_clause}'
                    rows = await conn.fetch(sql, *params)
                else:
                    sql = f'SELECT * FROM "{collection_name}"'
                    rows = await conn.fetch(sql)
                
                # 转换为字典列表
                data = [dict(row) for row in rows]
                
                if format.lower() == "json":
                    # JSON格式备份
                    with open(backup_file, 'w', encoding='utf-8') as f:
                        json.dump(data, f, indent=2, ensure_ascii=False, default=str)
                
                elif format.lower() == "csv":
                    # CSV格式备份
                    if data:
                        with open(backup_file, 'w', newline='', encoding='utf-8') as f:
                            writer = csv.DictWriter(f, fieldnames=data[0].keys())
                            writer.writeheader()
                            writer.writerows(data)
                
                else:
                    raise CleanupError(f"不支持的备份格式: {format}")
            
            return str(backup_file)
            
        except Exception as e:
            raise CleanupError(
                f"备份表失败: {e}",
                operation="backup_collection",
                target=collection_name
            )
    
    async def restore_collection(self, collection_name: str, 
                               backup_path: str,
                               format: str = "json") -> bool:
        """恢复表数据"""
        # PostgreSQL的恢复操作比较复杂，需要知道表结构
        # 这里提供基础实现，实际使用时可能需要根据具体需求调整
        raise CleanupError("PostgreSQL恢复功能需要根据具体表结构实现")
    
    def _build_where_clause(self, conditions: Dict[str, Any]) -> tuple:
        """构建WHERE子句"""
        if not conditions:
            return "TRUE", []
        
        clauses = []
        params = []
        param_index = 1
        
        for key, value in conditions.items():
            if isinstance(value, (list, tuple)):
                placeholders = ','.join([f'${param_index + i}' for i in range(len(value))])
                clauses.append(f'"{key}" = ANY(ARRAY[{placeholders}])')
                params.extend(value)
                param_index += len(value)
            else:
                clauses.append(f'"{key}" = ${param_index}')
                params.append(value)
                param_index += 1
        
        return " AND ".join(clauses), params
