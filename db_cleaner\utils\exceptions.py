#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库清理工具异常定义

定义了数据库清理工具中使用的所有自定义异常类。

作者: AI助手
日期: 2024-01-01
版本: 1.0.0
"""


class CleanerError(Exception):
    """数据库清理工具基础异常类"""
    
    def __init__(self, message, error_code=None, details=None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
    
    def __str__(self):
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message


class ConfigurationError(CleanerError):
    """配置相关异常"""
    
    def __init__(self, message, config_key=None, **kwargs):
        super().__init__(message, error_code="CONFIG_ERROR", **kwargs)
        self.config_key = config_key


class DatabaseConnectionError(CleanerError):
    """数据库连接异常"""
    
    def __init__(self, message, database_type=None, connection_params=None, **kwargs):
        super().__init__(message, error_code="DB_CONNECTION_ERROR", **kwargs)
        self.database_type = database_type
        self.connection_params = connection_params


class CleanupError(CleanerError):
    """清理操作异常"""
    
    def __init__(self, message, operation=None, target=None, **kwargs):
        super().__init__(message, error_code="CLEANUP_ERROR", **kwargs)
        self.operation = operation
        self.target = target


class ValidationError(CleanerError):
    """数据验证异常"""
    
    def __init__(self, message, field=None, value=None, **kwargs):
        super().__init__(message, error_code="VALIDATION_ERROR", **kwargs)
        self.field = field
        self.value = value


class BackupError(CleanerError):
    """备份操作异常"""
    
    def __init__(self, message, backup_path=None, **kwargs):
        super().__init__(message, error_code="BACKUP_ERROR", **kwargs)
        self.backup_path = backup_path


class RollbackError(CleanerError):
    """回滚操作异常"""
    
    def __init__(self, message, rollback_id=None, **kwargs):
        super().__init__(message, error_code="ROLLBACK_ERROR", **kwargs)
        self.rollback_id = rollback_id


class PermissionError(CleanerError):
    """权限相关异常"""
    
    def __init__(self, message, required_permission=None, **kwargs):
        super().__init__(message, error_code="PERMISSION_ERROR", **kwargs)
        self.required_permission = required_permission


class TimeoutError(CleanerError):
    """超时异常"""
    
    def __init__(self, message, timeout_seconds=None, **kwargs):
        super().__init__(message, error_code="TIMEOUT_ERROR", **kwargs)
        self.timeout_seconds = timeout_seconds


class UnsupportedOperationError(CleanerError):
    """不支持的操作异常"""
    
    def __init__(self, message, operation=None, database_type=None, **kwargs):
        super().__init__(message, error_code="UNSUPPORTED_OPERATION", **kwargs)
        self.operation = operation
        self.database_type = database_type
