#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
依赖注入容器模块

该模块提供依赖注入功能，用于管理组件间的依赖关系：
1. 服务注册和解析
2. 单例模式管理
3. 生命周期管理
4. 循环依赖检测
5. 服务替换和重置
6. 服务作用域管理

作者: AI助手
日期: 2023-06-01
版本: 2.0.0
"""

import inspect
import threading
from typing import Dict, Any, Type, TypeVar, Callable, Optional, Set, Generic, Union, cast, List, Protocol
from abc import ABC, abstractmethod
from enum import Enum
from dataclasses import dataclass
from contextlib import contextmanager
from src.utils.logger import get_logger
from src.utils.config_loader import ConfigLoader, ConfigError
from src.utils.event_system import EventSystem
from src.utils.async_manager import AsyncManager, get_async_manager
from src.data.db_manager import MongoDBManager, DatabaseConnectionPool
# 移除循环导入，改为延迟导入
# from .rule_engine import RuleEngine
# from .file_scanner import FileScanner
from src.video_analyzer import VideoAnalyzer
from .file_operations import FileOperations
from src.ui.factory import StandardUIFactory, IUIFactory

T = TypeVar('T')
TImpl = TypeVar('TImpl')

class DIError(Exception):
    """依赖注入异常基类"""
    pass

class ServiceNotFoundError(DIError):
    """服务未注册异常"""
    pass

class CircularDependencyError(DIError):
    """循环依赖异常"""
    pass

class ServiceResolutionError(DIError):
    """服务解析异常"""
    pass

class InvalidLifetimeError(DIError):
    """无效生命周期异常"""
    pass

class ServiceAlreadyRegisteredError(DIError):
    """服务已注册异常"""
    pass

class ServiceLifetime(Enum):
    """服务生命周期枚举"""
    SINGLETON = "singleton"  # 全局单例
    TRANSIENT = "transient"  # 每次解析创建新实例
    SCOPED = "scoped"        # 作用域内单例

    def __str__(self) -> str:
        return self.value


@dataclass
class ServiceDescriptor(Generic[T]):
    """服务描述符"""
    
    service_type: Type[T]
    implementation: Union[Type[T], Callable[[], T]]
    lifetime: ServiceLifetime = ServiceLifetime.TRANSIENT
    instance: Optional[T] = None
    factory: Optional[Callable[[], T]] = None


class IServiceProvider(Protocol):
    """服务提供者接口"""
    
    def get_service(self, service_type: Type[T]) -> T:
        """获取服务"""
        ...


class IServiceScope(IServiceProvider):
    """服务作用域接口"""
    
    def dispose(self) -> None:
        """释放资源"""
        ...


class ServiceScope(IServiceScope):
    """服务作用域实现"""
    
    def __init__(self, container: 'DIContainer'):
        self._container = container
        self._scoped_services: Dict[Type, Any] = {}
    
    def get_service(self, service_type: Type[T]) -> T:
        """获取服务"""
        return self._container._resolve_with_scope(service_type, self._scoped_services)
    
    def dispose(self) -> None:
        """释放资源"""
        # 调用所有作用域服务的dispose方法（如果有）
        for service in self._scoped_services.values():
            if hasattr(service, 'dispose') and callable(getattr(service, 'dispose')):
                service.dispose()
        self._scoped_services.clear()


class DIContainer:
    """依赖注入容器"""
    
    def __init__(self):
        self._services: Dict[Type, ServiceDescriptor] = {}
        self._singletons: Dict[Type, Any] = {}
        self._resolving: Set[Type] = set()
        self._lock = threading.RLock()  # 线程安全锁
        self._current_scope: Optional[ServiceScope] = None
    
    def register(self, service_type: Type[T], implementation: Optional[Union[Type[T], Callable[[], T]]] = None, 
                 lifetime: ServiceLifetime = ServiceLifetime.TRANSIENT) -> 'DIContainer':
        """统一的服务注册接口"""
        with self._lock:
            impl = implementation or service_type
            self._services[service_type] = ServiceDescriptor(service_type, impl, lifetime)
            return self
    
    def register_singleton(self, service_type: Type[T], implementation: Optional[Type[T]] = None) -> 'DIContainer':
        """注册单例服务"""
        return self.register(service_type, implementation, ServiceLifetime.SINGLETON)
    
    def register_transient(self, service_type: Type[T], implementation: Optional[Type[T]] = None) -> 'DIContainer':
        """注册瞬态服务"""
        return self.register(service_type, implementation, ServiceLifetime.TRANSIENT)
    
    def register_scoped(self, service_type: Type[T], implementation: Optional[Type[T]] = None) -> 'DIContainer':
        """注册作用域服务"""
        return self.register(service_type, implementation, ServiceLifetime.SCOPED)
    
    def register_instance(self, service_type: Type[T], instance: T) -> 'DIContainer':
        """注册实例"""
        with self._lock:
            self._singletons[service_type] = instance
            self._services[service_type] = ServiceDescriptor(service_type, type(instance), ServiceLifetime.SINGLETON)
        return self
    
    def register_factory(self, service_type: Type[T], factory: Callable[[], T], 
                         lifetime: ServiceLifetime = ServiceLifetime.TRANSIENT) -> 'DIContainer':
        """注册工厂方法"""
        with self._lock:
            descriptor = ServiceDescriptor(service_type, factory, lifetime)
            descriptor.factory = factory
            self._services[service_type] = descriptor
        return self
    
    def resolve(self, service_type: Type[T]) -> T:
        """解析服务"""
        with self._lock:
            if self._current_scope is not None:
                return self._current_scope.get_service(service_type)
            return self._resolve_internal(service_type)
    
    def _resolve_internal(self, service_type: Type[T]) -> T:
        """内部解析服务实现"""
        if service_type in self._resolving:
            service_name = getattr(service_type, '__name__', str(service_type))
            raise CircularDependencyError(f"检测到循环依赖: {service_name}")
        
        if service_type not in self._services:
            service_name = getattr(service_type, '__name__', str(service_type))
            raise ServiceNotFoundError(f"服务未注册: {service_name}")
        
        descriptor = self._services[service_type]
        
        # 单例模式
        if descriptor.lifetime == ServiceLifetime.SINGLETON:
            if service_type in self._singletons:
                return self._singletons[service_type]
            
            self._resolving.add(service_type)
            try:
                instance = self._create_instance(descriptor.implementation)
                self._singletons[service_type] = instance
                return instance
            finally:
                self._resolving.discard(service_type)
        
        # 瞬态模式
        elif descriptor.lifetime == ServiceLifetime.TRANSIENT:
            self._resolving.add(service_type)
            try:
                return self._create_instance(descriptor.implementation)
            finally:
                self._resolving.discard(service_type)
        
        # 作用域模式
        elif descriptor.lifetime == ServiceLifetime.SCOPED:
            raise ServiceResolutionError(f"作用域服务 {service_type} 必须在作用域内解析")
        
        raise InvalidLifetimeError(f"不支持的生命周期: {descriptor.lifetime}")
    
    def _resolve_with_scope(self, service_type: Type[T], scoped_services: Dict[Type, Any]) -> T:
        """在作用域内解析服务"""
        if service_type in self._resolving:
            service_name = getattr(service_type, '__name__', str(service_type))
            raise CircularDependencyError(f"检测到循环依赖: {service_name}")
        
        if service_type not in self._services:
            service_name = getattr(service_type, '__name__', str(service_type))
            raise ServiceNotFoundError(f"服务未注册: {service_name}")
        
        descriptor = self._services[service_type]
        
        # 单例模式
        if descriptor.lifetime == ServiceLifetime.SINGLETON:
            if service_type in self._singletons:
                return self._singletons[service_type]
            
            self._resolving.add(service_type)
            try:
                instance = self._create_instance(descriptor.implementation)
                self._singletons[service_type] = instance
                return instance
            finally:
                self._resolving.discard(service_type)
        
        # 作用域模式
        elif descriptor.lifetime == ServiceLifetime.SCOPED:
            if service_type in scoped_services:
                return scoped_services[service_type]
            
            self._resolving.add(service_type)
            try:
                instance = self._create_instance(descriptor.implementation)
                scoped_services[service_type] = instance
                return instance
            finally:
                self._resolving.discard(service_type)
        
        # 瞬态模式
        elif descriptor.lifetime == ServiceLifetime.TRANSIENT:
            self._resolving.add(service_type)
            try:
                return self._create_instance(descriptor.implementation)
            finally:
                self._resolving.discard(service_type)
        
        raise InvalidLifetimeError(f"不支持的生命周期: {descriptor.lifetime}")
    
    def _create_instance(self, implementation) -> Any:
        """创建实例，支持类型注解自动装配"""
        # 如果是工厂函数，直接调用
        if callable(implementation) and not inspect.isclass(implementation):
            return implementation()
        # 获取构造函数签名
        signature = inspect.signature(implementation.__init__)
        parameters = signature.parameters
        # 准备构造函数参数
        kwargs = {}
        for param_name, param in parameters.items():
            if param_name == 'self':
                continue
            # 跳过 *args 和 **kwargs 参数
            if param.kind in (inspect.Parameter.VAR_POSITIONAL, inspect.Parameter.VAR_KEYWORD):
                continue
            if param.annotation != inspect.Parameter.empty:
                # 递归解析依赖
                try:
                    kwargs[param_name] = self.resolve(param.annotation)
                except Exception as e:
                    # 类型注解无法resolve时，尝试用默认值
                    if param.default != inspect.Parameter.empty:
                        kwargs[param_name] = param.default
                    else:
                        raise ServiceResolutionError(f"无法自动装配参数 {param_name} 在 {implementation.__name__} 中: {e}")
            elif param.default != inspect.Parameter.empty:
                # 使用默认值
                kwargs[param_name] = param.default
            else:
                impl_name = getattr(implementation, '__name__', str(implementation))
                raise ServiceResolutionError(f"无法解析参数 {param_name} 在 {impl_name} 中")
        return implementation(**kwargs)
    
    def is_registered(self, service_type: Type) -> bool:
        """检查服务是否已注册"""
        return service_type in self._services
    
    def replace_service(self, service_type: Type[T], implementation: Union[Type[T], Callable[[], T]], 
                       lifetime: Optional[ServiceLifetime] = None) -> 'DIContainer':
        """替换已注册的服务"""
        with self._lock:
            if service_type not in self._services:
                raise ServiceNotFoundError(f"无法替换未注册的服务: {service_type}")
            
            old_descriptor = self._services[service_type]
            new_lifetime = lifetime or old_descriptor.lifetime
            
            # 如果是单例服务，移除旧实例
            if old_descriptor.lifetime == ServiceLifetime.SINGLETON and service_type in self._singletons:
                del self._singletons[service_type]
            
            # 注册新服务
            self._services[service_type] = ServiceDescriptor(service_type, implementation, new_lifetime)
            return self
    
    def create_scope(self) -> IServiceScope:
        """创建服务作用域"""
        return ServiceScope(self)
    
    @contextmanager
    def scope(self):
        """服务作用域上下文管理器"""
        previous_scope = self._current_scope
        scope = self.create_scope()
        self._current_scope = cast(ServiceScope, scope)  # 使用cast确保类型正确
        try:
            yield scope
        finally:
            if scope:
                scope.dispose()
            self._current_scope = previous_scope
    
    def clear(self):
        """清空容器"""
        with self._lock:
            # 调用所有单例服务的dispose方法（如果有）
            for service in self._singletons.values():
                if hasattr(service, 'dispose') and callable(getattr(service, 'dispose')):
                    service.dispose()
            
            self._services.clear()
            self._singletons.clear()
            self._resolving.clear()
            self._current_scope = None


# 全局容器实例
_container = DIContainer()


def get_container() -> DIContainer:
    """获取全局容器实例"""
    return _container


def configure_services(configurator: Callable[[DIContainer], None]):
    """配置服务"""
    configurator(_container)


def clear_container():
    """清空全局容器"""
    _container.clear()


def resolve(service_type: Type[T]) -> T:
    """解析服务的便捷方法"""
    return _container.resolve(service_type)


def register(service_type: Type[T], implementation_or_instance=None, 
            lifetime: ServiceLifetime = ServiceLifetime.TRANSIENT) -> None:
    """统一的服务注册便捷方法"""
    if implementation_or_instance is not None and not inspect.isclass(implementation_or_instance):
        # 如果是实例，注册为单例
        _container.register_instance(service_type, implementation_or_instance)
    else:
        # 否则根据指定的生命周期注册
        _container.register(service_type, implementation_or_instance, lifetime)


@contextmanager
def service_scope():
    """创建服务作用域的便捷方法"""
    with _container.scope() as scope:
        yield scope


# 为了兼容性，提供DependencyContainer别名
DependencyContainer = DIContainer

# 注册服务日志记录器 - 使用 get_logger 替代 ServiceLogger
# 彻底移除Logger相关注册

def configure_core_services():
    """
    注册核心服务到全局依赖注入容器，包括日志、事件系统、异步管理器、UI工厂、数据库、规则引擎、文件扫描、视频分析、文件操作等。
    """
    from src.utils.logger import get_logger
    from src.utils.event_system import EventSystem, get_event_system
    from src.utils.async_manager import AsyncManager, get_async_manager
    from src.utils.async_task_manager import AsyncTaskManager
    from src.utils.config_loader import ConfigLoader
    from src.data.db_manager import MongoDBManager
    # 延迟导入，避免循环依赖
    from .rule_engine import RuleEngine
    from .file_scanner import FileScanner
    from src.video_analyzer import VideoAnalyzer
    from .file_operations import FileOperations
    from src.ui.factory import StandardUIFactory, IUIFactory
    container = get_container()
    # 日志服务
    # 移除 from src.utils.logger import Logger, setup_logger
    # 移除 if not container.is_registered(Logger):
    # 移除 container.register_singleton(Logger, Logger)
    # 事件系统
    if not container.is_registered(EventSystem):
        container.register_singleton(EventSystem, EventSystem)
    # 异步管理器
    if not container.is_registered(AsyncManager):
        container.register_singleton(AsyncManager, AsyncManager)
    # 异步任务管理器
    if not container.is_registered(AsyncTaskManager):
        container.register_singleton(AsyncTaskManager, AsyncTaskManager)
    # 配置加载器
    if not container.is_registered(ConfigLoader):
        import os
        from pathlib import Path
        config_dir = Path(__file__).parent.parent.parent / "config"
        container.register_singleton(ConfigLoader, lambda: ConfigLoader(config_dir))
    # UI工厂
    if not container.is_registered(IUIFactory):
        container.register_singleton(IUIFactory, StandardUIFactory)
    # 数据库管理器
    if not container.is_registered(MongoDBManager):
        db_manager = MongoDBManager()
        container.register_instance(MongoDBManager, db_manager)
    # 注册RuleEngine服务（如未注册）
    if not container.is_registered(RuleEngine):
        import os
        rules_file = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../config/default_rules.yaml'))
        container.register_factory(RuleEngine, lambda: RuleEngine(rules_file), ServiceLifetime.SINGLETON)
    # 文件扫描器
    if not container.is_registered(FileScanner):
        container.register_factory(FileScanner, lambda: FileScanner(
            db_manager=container.resolve(MongoDBManager),
            async_task_manager=container.resolve(AsyncTaskManager)
        ), ServiceLifetime.SINGLETON)
    # 视频分析器
    if not container.is_registered(VideoAnalyzer):
        container.register_singleton(VideoAnalyzer, VideoAnalyzer)
    # 文件操作
    if not container.is_registered(FileOperations):
        container.register_singleton(FileOperations, FileOperations)