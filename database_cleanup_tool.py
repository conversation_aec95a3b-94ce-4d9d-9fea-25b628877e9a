#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库清理工具

该工具专门用于彻底清理智能文件管理器的数据库:
- 识别所有集合和数据残留
- 提供完整的数据清理方案
- 验证清理效果
- 修复数据残留问题

作者: AI助手
日期: 2024-01-01
版本: 1.0.0
"""

import asyncio
import time
from datetime import datetime
from typing import Dict, List, Any
from src.data.db_manager import MongoDBManager
from src.utils.logger import get_logger

logger = get_logger(__name__)


class DatabaseCleanupTool:
    """数据库清理工具"""
    
    def __init__(self):
        self.db_manager = None
        self.cleanup_report = {
            "start_time": None,
            "end_time": None,
            "collections_before": {},
            "collections_after": {},
            "indexes_before": {},
            "indexes_after": {},
            "cleanup_operations": [],
            "errors": [],
            "warnings": []
        }
    
    async def initialize(self):
        """初始化数据库连接"""
        try:
            self.db_manager = MongoDBManager()
            logger.info("数据库连接初始化成功")
            return True
        except Exception as e:
            logger.error(f"数据库连接初始化失败: {e}")
            return False
    
    async def analyze_database_state(self) -> Dict[str, Any]:
        """分析数据库当前状态"""
        try:
            logger.info("=== 分析数据库状态 ===")
            
            db = self.db_manager.db
            collection_names = db.list_collection_names()
            
            analysis = {
                "total_collections": len(collection_names),
                "collections": {},
                "total_documents": 0,
                "total_indexes": 0,
                "collection_details": []
            }
            
            for collection_name in collection_names:
                collection = db[collection_name]
                
                # 获取文档数量
                doc_count = collection.count_documents({})
                
                # 获取索引信息
                indexes = list(collection.list_indexes())
                index_count = len(indexes)
                
                # 获取样本数据
                sample_docs = list(collection.find({}).limit(2))
                
                # 获取集合统计信息
                try:
                    stats = db.command("collStats", collection_name)
                    size_info = {
                        "storage_size": stats.get("storageSize", 0),
                        "total_index_size": stats.get("totalIndexSize", 0),
                        "avg_obj_size": stats.get("avgObjSize", 0)
                    }
                except:
                    size_info = {"storage_size": 0, "total_index_size": 0, "avg_obj_size": 0}
                
                collection_info = {
                    "name": collection_name,
                    "document_count": doc_count,
                    "index_count": index_count,
                    "indexes": [idx.get("name", "unknown") for idx in indexes],
                    "sample_data": sample_docs,
                    "size_info": size_info
                }
                
                analysis["collections"][collection_name] = doc_count
                analysis["total_documents"] += doc_count
                analysis["total_indexes"] += index_count
                analysis["collection_details"].append(collection_info)
                
                logger.info(f"集合 {collection_name}: {doc_count} 文档, {index_count} 索引")
                if doc_count > 0:
                    logger.info(f"  样本数据: {sample_docs}")
            
            logger.info(f"数据库分析完成: {analysis['total_collections']} 个集合, "
                       f"{analysis['total_documents']} 个文档, {analysis['total_indexes']} 个索引")
            
            return analysis
            
        except Exception as e:
            logger.error(f"数据库状态分析失败: {e}")
            return {}
    
    async def perform_complete_cleanup(self, confirm: bool = False) -> bool:
        """执行完整的数据库清理"""
        try:
            if not confirm:
                logger.warning("请设置 confirm=True 来确认执行数据库清理操作")
                return False
            
            logger.info("=== 开始完整数据库清理 ===")
            self.cleanup_report["start_time"] = datetime.now()
            
            # 分析清理前状态
            before_state = await self.analyze_database_state()
            self.cleanup_report["collections_before"] = before_state.get("collections", {})
            
            db = self.db_manager.db
            collection_names = db.list_collection_names()
            
            # 清理所有集合的数据
            for collection_name in collection_names:
                try:
                    collection = db[collection_name]
                    
                    # 删除所有文档
                    delete_result = collection.delete_many({})
                    deleted_count = delete_result.deleted_count
                    
                    operation_info = {
                        "collection": collection_name,
                        "operation": "delete_documents",
                        "deleted_count": deleted_count,
                        "success": True
                    }
                    
                    self.cleanup_report["cleanup_operations"].append(operation_info)
                    logger.info(f"清理集合 {collection_name}: 删除 {deleted_count} 个文档")
                    
                except Exception as e:
                    error_info = f"清理集合 {collection_name} 失败: {e}"
                    self.cleanup_report["errors"].append(error_info)
                    logger.error(error_info)
            
            # 可选：删除集合（谨慎操作）
            # for collection_name in collection_names:
            #     if collection_name not in ["system_collections"]:  # 保留系统集合
            #         try:
            #             db.drop_collection(collection_name)
            #             logger.info(f"删除集合: {collection_name}")
            #         except Exception as e:
            #             logger.error(f"删除集合 {collection_name} 失败: {e}")
            
            # 等待操作完成
            await asyncio.sleep(2)
            
            # 分析清理后状态
            after_state = await self.analyze_database_state()
            self.cleanup_report["collections_after"] = after_state.get("collections", {})
            self.cleanup_report["end_time"] = datetime.now()
            
            # 验证清理效果
            total_remaining = sum(self.cleanup_report["collections_after"].values())
            cleanup_success = total_remaining == 0
            
            if cleanup_success:
                logger.info("✅ 数据库清理完成，所有数据已清除")
            else:
                logger.warning(f"⚠️ 数据库清理完成，但仍有 {total_remaining} 个文档残留")
                
                # 分析残留数据
                for collection_name, count in self.cleanup_report["collections_after"].items():
                    if count > 0:
                        self.cleanup_report["warnings"].append(
                            f"集合 {collection_name} 仍有 {count} 个文档"
                        )
            
            return cleanup_success
            
        except Exception as e:
            error_msg = f"数据库清理过程中出现异常: {e}"
            self.cleanup_report["errors"].append(error_msg)
            logger.error(error_msg)
            return False
    
    async def cleanup_specific_collections(self, collection_names: List[str], 
                                         confirm: bool = False) -> bool:
        """清理指定的集合"""
        try:
            if not confirm:
                logger.warning("请设置 confirm=True 来确认执行清理操作")
                return False
            
            logger.info(f"=== 清理指定集合: {collection_names} ===")
            
            db = self.db_manager.db
            success_count = 0
            
            for collection_name in collection_names:
                try:
                    if collection_name in db.list_collection_names():
                        collection = db[collection_name]
                        delete_result = collection.delete_many({})
                        deleted_count = delete_result.deleted_count
                        
                        logger.info(f"清理集合 {collection_name}: 删除 {deleted_count} 个文档")
                        success_count += 1
                    else:
                        logger.warning(f"集合 {collection_name} 不存在")
                        
                except Exception as e:
                    logger.error(f"清理集合 {collection_name} 失败: {e}")
            
            return success_count == len(collection_names)
            
        except Exception as e:
            logger.error(f"清理指定集合失败: {e}")
            return False
    
    async def fix_data_residue_issues(self) -> bool:
        """修复数据残留问题"""
        try:
            logger.info("=== 修复数据残留问题 ===")
            
            # 分析当前状态
            current_state = await self.analyze_database_state()
            
            # 识别有问题的集合
            problematic_collections = []
            for collection_name, doc_count in current_state["collections"].items():
                if doc_count > 0:
                    problematic_collections.append(collection_name)
            
            if not problematic_collections:
                logger.info("没有发现数据残留问题")
                return True
            
            logger.info(f"发现有数据残留的集合: {problematic_collections}")
            
            # 分析残留数据的特征
            db = self.db_manager.db
            for collection_name in problematic_collections:
                collection = db[collection_name]
                
                # 获取残留数据样本
                sample_docs = list(collection.find({}).limit(5))
                logger.info(f"集合 {collection_name} 残留数据样本:")
                for i, doc in enumerate(sample_docs):
                    logger.info(f"  文档 {i+1}: {doc}")
                
                # 尝试分析数据来源
                if sample_docs:
                    first_doc = sample_docs[0]
                    if "_id" in first_doc:
                        logger.info(f"  最早文档ID: {first_doc['_id']}")
                    if "created_time" in first_doc:
                        logger.info(f"  创建时间: {first_doc['created_time']}")
                    if "path" in first_doc:
                        logger.info(f"  路径信息: {first_doc['path']}")
            
            # 提供修复建议
            logger.info("=== 修复建议 ===")
            logger.info("1. 手动清理残留数据:")
            for collection_name in problematic_collections:
                logger.info(f"   db.{collection_name}.deleteMany({{}})")
            
            logger.info("2. 或者使用工具自动清理:")
            logger.info("   await cleanup_tool.cleanup_specific_collections(problematic_collections, confirm=True)")
            
            return True
            
        except Exception as e:
            logger.error(f"修复数据残留问题失败: {e}")
            return False
    
    def get_cleanup_report(self) -> str:
        """获取清理报告"""
        if not self.cleanup_report["start_time"]:
            return "尚未执行清理操作"
        
        duration = (self.cleanup_report["end_time"] - self.cleanup_report["start_time"]).total_seconds()
        
        report = f"""
=== 数据库清理报告 ===
开始时间: {self.cleanup_report["start_time"].strftime('%Y-%m-%d %H:%M:%S')}
结束时间: {self.cleanup_report["end_time"].strftime('%Y-%m-%d %H:%M:%S')}
清理耗时: {duration:.2f} 秒

清理前状态:
"""
        
        for collection, count in self.cleanup_report["collections_before"].items():
            report += f"  {collection}: {count} 个文档\n"
        
        report += "\n清理后状态:\n"
        for collection, count in self.cleanup_report["collections_after"].items():
            report += f"  {collection}: {count} 个文档\n"
        
        if self.cleanup_report["cleanup_operations"]:
            report += "\n清理操作:\n"
            for op in self.cleanup_report["cleanup_operations"]:
                report += f"  {op['collection']}: 删除 {op['deleted_count']} 个文档\n"
        
        if self.cleanup_report["errors"]:
            report += "\n错误信息:\n"
            for error in self.cleanup_report["errors"]:
                report += f"  - {error}\n"
        
        if self.cleanup_report["warnings"]:
            report += "\n警告信息:\n"
            for warning in self.cleanup_report["warnings"]:
                report += f"  - {warning}\n"
        
        total_before = sum(self.cleanup_report["collections_before"].values())
        total_after = sum(self.cleanup_report["collections_after"].values())
        
        report += f"\n总结:\n"
        report += f"  清理前总文档数: {total_before}\n"
        report += f"  清理后总文档数: {total_after}\n"
        report += f"  清理成功率: {((total_before - total_after) / total_before * 100) if total_before > 0 else 100:.1f}%\n"
        
        return report


async def main():
    """主函数"""
    cleanup_tool = DatabaseCleanupTool()
    
    # 初始化
    if not await cleanup_tool.initialize():
        logger.error("初始化失败")
        return False
    
    # 分析数据库状态
    logger.info("正在分析数据库状态...")
    analysis = await cleanup_tool.analyze_database_state()
    
    if analysis["total_documents"] == 0:
        logger.info("数据库已经是空的，无需清理")
        return True
    
    # 修复数据残留问题
    await cleanup_tool.fix_data_residue_issues()
    
    # 询问是否执行清理
    print(f"\n发现 {analysis['total_documents']} 个文档需要清理")
    print("是否执行完整清理？(输入 'yes' 确认)")
    
    # 在脚本中自动确认清理
    user_input = "yes"  # 可以改为 input() 来手动确认
    
    if user_input.lower() == "yes":
        logger.info("开始执行数据库清理...")
        success = await cleanup_tool.perform_complete_cleanup(confirm=True)
        
        # 输出清理报告
        print(cleanup_tool.get_cleanup_report())
        
        if success:
            logger.info("✅ 数据库清理成功完成")
        else:
            logger.warning("⚠️ 数据库清理完成，但可能仍有残留")
        
        return success
    else:
        logger.info("用户取消了清理操作")
        return False


if __name__ == "__main__":
    # 运行清理工具
    result = asyncio.run(main())
    exit(0 if result else 1)
