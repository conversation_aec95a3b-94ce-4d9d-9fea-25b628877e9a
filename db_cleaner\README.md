# Database Cleaner - 通用数据库清理工具

一个强大、灵活、可重用的数据库清理工具，支持多种数据库类型和清理策略。

## 🚀 特性

- **多数据库支持**: MongoDB、MySQL、PostgreSQL
- **灵活配置**: 支持YAML、JSON、TOML配置文件
- **安全可靠**: 自动备份、确认机制、回滚功能
- **高性能**: 批量操作、异步处理、进度跟踪
- **易于使用**: CLI和API两种使用方式
- **可扩展**: 插件化架构，易于添加新的数据库支持

## 📦 安装

### 基础安装
```bash
pip install db-cleaner
```

### 数据库依赖
根据需要安装对应的数据库驱动：

```bash
# MongoDB
pip install motor pymongo

# MySQL
pip install aiomysql pymysql

# PostgreSQL
pip install asyncpg
```

### 开发安装
```bash
git clone https://github.com/your-repo/db-cleaner.git
cd db-cleaner
pip install -e .
```

## 🔧 快速开始

### 命令行使用

#### 1. 生成配置文件
```bash
db-cleaner generate-config --type mongodb --output config.yaml
```

#### 2. 编辑配置文件
```yaml
database:
  type: mongodb
  host: localhost
  port: 27017
  database: your_database

rules:
  - target: collection_name
    conditions: {}
    backup: true
    operation: delete
```

#### 3. 执行清理
```bash
# 使用配置文件
db-cleaner clean --config config.yaml

# 快速清理
db-cleaner clean --type mongodb --host localhost --database test --collections files,folders --confirm
```

### 编程接口使用

#### 基础用法
```python
import asyncio
from db_cleaner import create_cleaner

async def main():
    # 创建清理器
    cleaner = create_cleaner(
        database_type="mongodb",
        connection_params={
            "host": "localhost",
            "port": 27017,
            "database": "test"
        }
    )
    
    # 执行清理
    async with cleaner:
        report = await cleaner.clean_selective(
            targets=["files", "folders"],
            confirm=True
        )
        
        print(report.get_summary())

asyncio.run(main())
```

#### 使用配置文件
```python
from db_cleaner import CleanerConfig, CleanerFactory

# 从配置文件创建
config = CleanerConfig.from_file("config.yaml")
cleaner = CleanerFactory.create_cleaner(config)

# 执行清理
async with cleaner:
    report = await cleaner.clean_all()
```

## 📖 详细文档

### 配置文件格式

#### 数据库配置
```yaml
database:
  type: mongodb  # mongodb, mysql, postgresql
  host: localhost
  port: 27017
  username: user
  password: pass
  database: dbname
  # 或使用连接URI
  uri: "******************************************"
```

#### 清理规则
```yaml
rules:
  - target: collection_name    # 目标集合/表
    conditions:               # 清理条件
      field: value
      date_field:
        $lt: "2024-01-01"
    backup: true             # 是否备份
    operation: delete        # delete 或 truncate
    batch_size: 1000        # 批处理大小
    timeout: 60             # 超时时间（秒）
```

#### 备份配置
```yaml
backup:
  enabled: true
  directory: "./backups"
  format: json              # json, csv, bson
  compress: true
  keep_count: 5
```

### 命令行参考

#### 清理命令
```bash
# 基础清理
db-cleaner clean --config config.yaml

# 选择性清理
db-cleaner clean --type mongodb --host localhost --database test \
  --collections files,folders --conditions '{"status": "deleted"}' \
  --backup --confirm

# 试运行
db-cleaner clean --config config.yaml --dry-run
```

#### 分析命令
```bash
# 分析数据库结构
db-cleaner analyze --type mongodb --host localhost --database test

# 保存分析结果
db-cleaner analyze --config config.yaml --output analysis.json
```

#### 备份命令
```bash
# 备份指定集合
db-cleaner backup --type mongodb --host localhost --database test \
  --collections files,folders --output ./backup --format json

# 备份所有集合
db-cleaner backup --config config.yaml --output ./backup
```

#### 其他命令
```bash
# 列出集合/表
db-cleaner list --type mongodb --host localhost --database test

# 显示工具信息
db-cleaner info

# 生成配置模板
db-cleaner generate-config --type mysql --output mysql_config.yaml
```

### API参考

#### 核心类

##### DatabaseCleaner
```python
class DatabaseCleaner:
    async def connect() -> bool
    async def disconnect() -> None
    async def clean_all(confirm: bool = None) -> CleanupReport
    async def clean_selective(targets: List[str], conditions: Dict = None, confirm: bool = None) -> CleanupReport
    async def backup_targets(targets: List[str], backup_dir: str = None) -> List[str]
    async def discover_database() -> Dict[str, Any]
    async def analyze_targets(targets: List[str] = None) -> Dict[str, Any]
```

##### CleanerConfig
```python
class CleanerConfig:
    @classmethod
    def from_file(cls, file_path: str) -> 'CleanerConfig'
    @classmethod
    def from_dict(cls, config_dict: Dict) -> 'CleanerConfig'
    def save_to_file(self, file_path: str, format: str = None) -> None
    def to_dict(self) -> Dict[str, Any]
```

##### CleanerFactory
```python
class CleanerFactory:
    @classmethod
    def create_cleaner(cls, config: CleanerConfig) -> DatabaseCleaner
    @classmethod
    def create_adapter(cls, database_type: str, connection_params: Dict) -> DatabaseAdapter
    @classmethod
    def register_adapter(cls, database_type: str, adapter_class: Type[DatabaseAdapter])
```

#### 便捷函数
```python
# 快速创建清理器
cleaner = create_cleaner(config_path="config.yaml")

# 快速清理
report = quick_clean(
    database_type="mongodb",
    connection_params={"host": "localhost", "database": "test"},
    collections=["files", "folders"],
    confirm=True
)
```

## 🔌 扩展开发

### 添加新的数据库支持

1. 继承 `DatabaseAdapter` 基类
2. 实现所有抽象方法
3. 注册适配器

```python
from db_cleaner.adapters.base import DatabaseAdapter
from db_cleaner.core.factory import CleanerFactory

class CustomDatabaseAdapter(DatabaseAdapter):
    async def connect(self) -> bool:
        # 实现连接逻辑
        pass
    
    # 实现其他必需方法...

# 注册适配器
CleanerFactory.register_adapter("custom_db", CustomDatabaseAdapter)
```

### 自定义清理规则

```python
from db_cleaner.core.config import CleanupRuleConfig

# 创建自定义规则
custom_rule = CleanupRuleConfig(
    target="my_collection",
    conditions={"custom_field": "custom_value"},
    backup=True,
    operation="delete",
    batch_size=500
)

config.rules.append(custom_rule)
```

## 🛡️ 安全特性

- **确认机制**: 默认需要用户确认危险操作
- **自动备份**: 清理前自动创建备份
- **批量限制**: 限制单次删除的最大数量
- **试运行模式**: 可以预览操作而不执行
- **受保护集合**: 可以设置不允许清理的集合
- **操作日志**: 详细记录所有操作

## 📊 性能优化

- **异步操作**: 全面支持异步处理
- **批量处理**: 大数据量分批处理
- **连接池**: 数据库连接池管理
- **进度跟踪**: 实时显示操作进度
- **内存优化**: 流式处理大数据集

## 🐛 故障排除

### 常见问题

1. **连接失败**
   - 检查数据库服务是否运行
   - 验证连接参数是否正确
   - 确认网络连接

2. **权限错误**
   - 确认数据库用户有足够权限
   - 检查防火墙设置

3. **依赖缺失**
   - 安装对应的数据库驱动
   - 检查Python版本兼容性

### 调试模式
```bash
db-cleaner clean --config config.yaml --log-level DEBUG
```

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📞 支持

- 文档: [https://db-cleaner.readthedocs.io](https://db-cleaner.readthedocs.io)
- Issues: [https://github.com/your-repo/db-cleaner/issues](https://github.com/your-repo/db-cleaner/issues)
- 讨论: [https://github.com/your-repo/db-cleaner/discussions](https://github.com/your-repo/db-cleaner/discussions)
