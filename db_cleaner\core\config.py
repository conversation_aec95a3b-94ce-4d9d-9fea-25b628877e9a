#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库清理工具配置系统

提供灵活的配置管理，支持从文件、环境变量和命令行参数加载配置。

作者: AI助手
日期: 2024-01-01
版本: 1.0.0
"""

import os
import json
import yaml
import toml
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field, asdict

from ..utils.exceptions import ConfigurationError


@dataclass
class DatabaseConfig:
    """数据库连接配置"""
    
    # 数据库类型
    type: str
    
    # 连接参数
    host: str = "localhost"
    port: int = None
    username: str = None
    password: str = None
    database: str = None
    
    # 连接选项
    options: Dict[str, Any] = field(default_factory=dict)
    
    # 连接字符串（如果提供，将优先使用）
    uri: str = None
    
    def __post_init__(self):
        """验证配置"""
        if not self.type:
            raise ConfigurationError("数据库类型不能为空")
        
        # 设置默认端口
        if not self.port:
            default_ports = {
                "mongodb": 27017,
                "mysql": 3306,
                "postgresql": 5432,
                "sqlite": None
            }
            self.port = default_ports.get(self.type.lower())
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {k: v for k, v in asdict(self).items() if v is not None}
    
    def get_connection_params(self) -> Dict[str, Any]:
        """获取连接参数"""
        if self.uri:
            return {"uri": self.uri}
        
        params = {
            "host": self.host,
            "port": self.port
        }
        
        if self.username:
            params["username"] = self.username
        
        if self.password:
            params["password"] = self.password
        
        if self.database:
            params["database"] = self.database
        
        # 添加其他选项
        params.update(self.options)
        
        return params


@dataclass
class CleanupRuleConfig:
    """清理规则配置"""
    
    # 目标集合/表
    target: str
    
    # 清理条件
    conditions: Dict[str, Any] = field(default_factory=dict)
    
    # 清理前备份
    backup: bool = True
    
    # 清理操作（delete/truncate）
    operation: str = "delete"
    
    # 批处理大小
    batch_size: int = 1000
    
    # 超时设置（秒）
    timeout: int = 60
    
    def __post_init__(self):
        """验证配置"""
        if not self.target:
            raise ConfigurationError("清理目标不能为空")
        
        valid_operations = ["delete", "truncate"]
        if self.operation not in valid_operations:
            raise ConfigurationError(
                f"无效的清理操作: {self.operation}，有效值: {valid_operations}"
            )


@dataclass
class BackupConfig:
    """备份配置"""
    
    # 是否启用备份
    enabled: bool = True
    
    # 备份目录
    directory: str = "./backups"
    
    # 备份格式（json/bson/csv）
    format: str = "json"
    
    # 压缩备份
    compress: bool = True
    
    # 保留备份数量
    keep_count: int = 5
    
    def __post_init__(self):
        """验证配置"""
        if self.enabled:
            valid_formats = ["json", "bson", "csv"]
            if self.format not in valid_formats:
                raise ConfigurationError(
                    f"无效的备份格式: {self.format}，有效值: {valid_formats}"
                )


@dataclass
class ScheduleConfig:
    """定时任务配置"""
    
    # 是否启用定时任务
    enabled: bool = False
    
    # 定时表达式（cron格式）
    cron: str = None
    
    # 间隔时间（秒）
    interval: int = None
    
    # 指定时间点
    at: str = None
    
    def __post_init__(self):
        """验证配置"""
        if self.enabled:
            if not any([self.cron, self.interval, self.at]):
                raise ConfigurationError("定时任务需要指定cron、interval或at中的一个")


@dataclass
class LoggingConfig:
    """日志配置"""
    
    # 日志级别
    level: str = "INFO"
    
    # 日志文件
    file: str = None
    
    # 是否启用控制台颜色
    color: bool = True
    
    # 日志格式
    format: str = "%(asctime)s [%(levelname)s] [%(name)s] %(message)s"
    
    def __post_init__(self):
        """验证配置"""
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if self.level.upper() not in valid_levels:
            raise ConfigurationError(
                f"无效的日志级别: {self.level}，有效值: {valid_levels}"
            )


@dataclass
class CleanerConfig:
    """清理工具主配置"""
    
    # 数据库配置
    database: DatabaseConfig = None
    
    # 清理规则
    rules: List[CleanupRuleConfig] = field(default_factory=list)
    
    # 备份配置
    backup: BackupConfig = field(default_factory=BackupConfig)
    
    # 定时任务配置
    schedule: ScheduleConfig = field(default_factory=ScheduleConfig)
    
    # 日志配置
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    
    # 安全设置
    safety: Dict[str, Any] = field(default_factory=lambda: {
        "confirm_cleanup": True,
        "dry_run": False,
        "protected_collections": [],
        "max_delete_count": 10000
    })
    
    # 高级选项
    options: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """初始化后处理"""
        # 如果数据库配置是字典，转换为对象
        if isinstance(self.database, dict):
            self.database = DatabaseConfig(**self.database)
        
        # 如果规则是字典列表，转换为对象
        if self.rules and isinstance(self.rules[0], dict):
            self.rules = [CleanupRuleConfig(**rule) for rule in self.rules]
        
        # 如果备份配置是字典，转换为对象
        if isinstance(self.backup, dict):
            self.backup = BackupConfig(**self.backup)
        
        # 如果定时任务配置是字典，转换为对象
        if isinstance(self.schedule, dict):
            self.schedule = ScheduleConfig(**self.schedule)
        
        # 如果日志配置是字典，转换为对象
        if isinstance(self.logging, dict):
            self.logging = LoggingConfig(**self.logging)
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'CleanerConfig':
        """从字典创建配置"""
        # 处理数据库配置
        if "database" in config_dict:
            config_dict["database"] = DatabaseConfig(**config_dict["database"])
        
        # 处理规则配置
        if "rules" in config_dict:
            config_dict["rules"] = [CleanupRuleConfig(**rule) for rule in config_dict["rules"]]
        
        # 处理备份配置
        if "backup" in config_dict:
            config_dict["backup"] = BackupConfig(**config_dict["backup"])
        
        # 处理定时任务配置
        if "schedule" in config_dict:
            config_dict["schedule"] = ScheduleConfig(**config_dict["schedule"])
        
        # 处理日志配置
        if "logging" in config_dict:
            config_dict["logging"] = LoggingConfig(**config_dict["logging"])
        
        return cls(**config_dict)
    
    @classmethod
    def from_file(cls, file_path: Union[str, Path]) -> 'CleanerConfig':
        """从文件加载配置"""
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise ConfigurationError(f"配置文件不存在: {file_path}")
        
        # 根据文件扩展名选择解析器
        parsers = {
            ".json": json.load,
            ".yaml": yaml.safe_load,
            ".yml": yaml.safe_load,
            ".toml": toml.load
        }
        
        ext = file_path.suffix.lower()
        if ext not in parsers:
            raise ConfigurationError(f"不支持的配置文件格式: {ext}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                config_dict = parsers[ext](f)
            
            return cls.from_dict(config_dict)
        except Exception as e:
            raise ConfigurationError(f"解析配置文件失败: {e}")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {}
        
        # 处理数据库配置
        if self.database:
            result["database"] = self.database.to_dict()
        
        # 处理规则配置
        if self.rules:
            result["rules"] = [asdict(rule) for rule in self.rules]
        
        # 处理备份配置
        result["backup"] = asdict(self.backup)
        
        # 处理定时任务配置
        result["schedule"] = asdict(self.schedule)
        
        # 处理日志配置
        result["logging"] = asdict(self.logging)
        
        # 处理安全设置和选项
        result["safety"] = self.safety
        result["options"] = self.options
        
        return result
    
    def save_to_file(self, file_path: Union[str, Path], format: str = None) -> None:
        """保存配置到文件"""
        file_path = Path(file_path)
        
        # 确保目录存在
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 确定格式
        if format is None:
            format = file_path.suffix.lstrip('.')
        
        config_dict = self.to_dict()
        
        # 根据格式选择序列化方法
        if format in ['yaml', 'yml']:
            with open(file_path, 'w', encoding='utf-8') as f:
                yaml.dump(config_dict, f, default_flow_style=False)
        elif format == 'json':
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=2)
        elif format == 'toml':
            with open(file_path, 'w', encoding='utf-8') as f:
                toml.dump(config_dict, f)
        else:
            raise ConfigurationError(f"不支持的配置文件格式: {format}")
    
    def get_target_collections(self) -> List[str]:
        """获取所有目标集合/表"""
        return [rule.target for rule in self.rules]
