#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
文件树索引服务（重构版）

该模块实现以文件夹为核心的文件树索引服务:
- FileTreeIndexService: 文件树索引服务类
- 支持文件夹为核心的层次化结构
- 保持懒加载、缓存和虚拟节点兼容性
- 集成新的哈希计算和一致性检查机制

作者: AI助手
日期: 2024-01-01
版本: 2.0.0
"""

from typing import List, Dict, Any, Optional, Callable, Union, Tuple
from src.data.models import FileInfo
from src.data.dto.folder_dto import FolderDTO
from src.data.folder_repository import FolderRepository
from src.core.folder_hash_manager import FolderHashManager
from src.utils.consistency_checker import ConsistencyChecker
from src.utils.logger import get_logger
import asyncio
from src.core.progress_tracker import ProgressTracker

logger = get_logger(__name__)


class FileTreeIndexService:
    """
    文件树索引服务（重构版）

    实现以文件夹为核心的文件树索引，支持懒加载、缓存和一致性检查
    """
    def __init__(self, db_manager):
        """
        初始化文件树索引服务

        参数:
            db_manager: 数据库管理器
        """
        self.db_manager = db_manager
        self.logger = logger

        # 初始化核心组件
        self.folder_repository = FolderRepository(db_manager)
        self.hash_manager = FolderHashManager(cache_size=5000, enable_cache=True)
        self.consistency_checker = ConsistencyChecker(self.folder_repository, self.hash_manager)

        # 缓存系统
        self._folder_cache = {}  # 文件夹缓存
        self._file_cache = {}    # 文件缓存
        self._children_cache = {}  # 子项缓存

        # 懒加载配置
        self.lazy_loading_enabled = True
        self.virtual_node_threshold = 100  # 虚拟节点阈值

        # 统计信息
        self.cache_hits = 0
        self.cache_misses = 0

    async def get_folder_children_async(self, folder_id: Optional[str]) -> Dict[str, Any]:
        """
        异步获取文件夹的直接子项（文件夹 + 文件）

        参数:
            folder_id: 文件夹ID（None表示根文件夹）

        返回:
            包含子文件夹和文件的字典
        """
        try:
            # 检查缓存
            cache_key = f"children_{folder_id}"
            if cache_key in self._children_cache:
                self.cache_hits += 1
                return self._children_cache[cache_key]

            self.cache_misses += 1

            # 获取子文件夹
            child_folders = await self.folder_repository.get_child_folders(folder_id)

            # 获取文件夹内的文件
            files = await self._get_folder_files(folder_id)

            # 构建结果
            result = {
                "folders": child_folders,
                "files": files,
                "total_count": len(child_folders) + len(files),
                "folder_count": len(child_folders),
                "file_count": len(files)
            }

            # 更新缓存
            self._children_cache[cache_key] = result

            return result

        except Exception as e:
            self.logger.error(f"获取文件夹子项失败 folder_id={folder_id}: {e}")
            return {
                "folders": [],
                "files": [],
                "total_count": 0,
                "folder_count": 0,
                "file_count": 0
            }

    async def _get_folder_files(self, folder_id: Optional[str]) -> List[FileInfo]:
        """
        获取文件夹内的文件列表

        参数:
            folder_id: 文件夹ID

        返回:
            文件信息列表
        """
        try:
            # 构建查询条件
            if folder_id:
                # 查询指定文件夹内的文件
                query = {"folder_id": folder_id}
            else:
                # 查询根目录文件（没有folder_id或folder_id为None）
                query = {"$or": [{"folder_id": None}, {"folder_id": {"$exists": False}}]}

            # 执行查询
            docs = await asyncio.to_thread(self.db_manager.find_files, query)

            # 转换为FileInfo对象
            files = []
            for doc in docs:
                try:
                    file_info = FileInfo.from_dict(doc)
                    files.append(file_info)
                except Exception as e:
                    self.logger.warning(f"转换文件信息失败: {e}")
                    continue

            return files

        except Exception as e:
            self.logger.error(f"获取文件夹文件失败 folder_id={folder_id}: {e}")
            return []

    async def get_children_async(self, parent_id: Optional[str]) -> List[FileInfo]:
        """
        异步获取某节点的直接子节点（兼容性方法）

        参数:
            parent_id: 父节点ID

        返回:
            子节点列表（FileInfo格式）
        """
        try:
            # 获取文件夹子项
            children_data = await self.get_folder_children_async(parent_id)

            # 转换为FileInfo列表（为了兼容性）
            result = []

            # 添加子文件夹（转换为FileInfo格式）
            for folder in children_data["folders"]:
                # 创建文件夹的FileInfo表示
                folder_file_info = FileInfo(
                    file_path=folder.path,
                    name=folder.name,
                    size=folder.total_size,
                    modified_time=folder.modified_time or folder.created_time,
                    created_time=folder.created_time or folder.modified_time,
                    extension="",  # 文件夹没有扩展名
                    file_id=folder.folder_id,
                    parent_id=folder.parent_id,
                    depth=folder.depth,
                    metadata={"is_folder": True, "folder_dto": folder}
                )
                result.append(folder_file_info)

            # 添加文件
            result.extend(children_data["files"])

            return result

        except Exception as e:
            self.logger.error(f"获取子节点失败 parent_id={parent_id}: {e}")
            return []

    async def build_folder_index_async(self, file_list: List[FileInfo],
                                     progress_callback: Optional[Callable] = None) -> bool:
        """
        构建文件夹索引（新的核心方法）

        参数:
            file_list: 文件信息列表
            progress_callback: 进度回调函数

        返回:
            是否构建成功
        """
        try:
            self.logger.info(f"开始构建文件夹索引，文件数量: {len(file_list)}")

            # 阶段1：提取文件夹信息
            tracker = ProgressTracker(total_items=len(file_list), stages=["提取文件夹", "构建层次", "计算哈希", "保存数据"])

            folders_map = await self._extract_folders_from_files(file_list, tracker, progress_callback)

            # 阶段2：构建文件夹层次关系
            await self._build_folder_hierarchy(folders_map, tracker, progress_callback)

            # 阶段3：计算文件夹哈希
            await self._calculate_folder_hashes(folders_map, tracker, progress_callback)

            # 阶段4：批量保存文件夹数据
            success = await self._save_folders_batch(list(folders_map.values()), tracker, progress_callback)

            if success:
                self.logger.info(f"文件夹索引构建完成，创建了{len(folders_map)}个文件夹")
                # 清空缓存
                self._clear_cache()
            else:
                self.logger.error("文件夹索引构建失败")

            return success

        except Exception as e:
            self.logger.error(f"构建文件夹索引失败: {e}")
            return False

    async def _extract_folders_from_files(self, file_list: List[FileInfo],
                                        tracker: ProgressTracker,
                                        progress_callback: Optional[Callable] = None) -> Dict[str, FolderDTO]:
        """
        从文件列表中提取文件夹信息

        参数:
            file_list: 文件信息列表
            tracker: 进度跟踪器
            progress_callback: 进度回调函数

        返回:
            文件夹路径到FolderDTO的映射
        """
        folders_map = {}

        try:
            for i, file_info in enumerate(file_list):
                try:
                    # 获取文件所在的文件夹路径
                    import os
                    folder_path = os.path.dirname(file_info.file_path)

                    if folder_path and folder_path not in folders_map:
                        # 创建文件夹DTO
                        folder_dto = FolderDTO.from_path(folder_path)
                        folders_map[folder_path] = folder_dto

                    # 更新进度
                    tracker.update(1)
                    if progress_callback and i % 100 == 0:  # 每100个文件更新一次进度
                        progress_info = tracker.get_progress_info()
                        await asyncio.get_event_loop().run_in_executor(
                            None,
                            lambda: progress_callback(
                                progress_info["progress"],
                                f"提取文件夹: {i+1}/{len(file_list)}",
                                file_info.file_path,
                                i+1,
                                len(file_list),
                                progress_info["elapsed_time"]
                            )
                        )

                except Exception as e:
                    self.logger.warning(f"处理文件失败 {file_info.file_path}: {e}")
                    continue

            self.logger.info(f"从{len(file_list)}个文件中提取了{len(folders_map)}个文件夹")
            return folders_map

        except Exception as e:
            self.logger.error(f"提取文件夹信息失败: {e}")
            return {}

    async def _build_folder_hierarchy(self, folders_map: Dict[str, FolderDTO],
                                    tracker: ProgressTracker,
                                    progress_callback: Optional[Callable] = None) -> None:
        """
        构建文件夹层次关系

        参数:
            folders_map: 文件夹映射
            tracker: 进度跟踪器
            progress_callback: 进度回调函数
        """
        try:
            # 按路径长度排序，确保父文件夹先处理
            sorted_folders = sorted(folders_map.items(), key=lambda x: len(x[0].split('/')))

            for i, (folder_path, folder_dto) in enumerate(sorted_folders):
                try:
                    # 查找父文件夹
                    import os
                    parent_path = os.path.dirname(folder_path)

                    if parent_path and parent_path in folders_map:
                        parent_folder = folders_map[parent_path]

                        # 设置父子关系
                        folder_dto.parent_id = parent_folder.folder_id
                        folder_dto.depth = parent_folder.depth + 1

                        # 添加到父文件夹的子文件夹列表
                        parent_folder.add_child_folder(folder_dto.folder_id)
                    else:
                        # 根文件夹
                        folder_dto.parent_id = None
                        folder_dto.depth = 1

                    # 更新进度
                    if progress_callback and i % 50 == 0:
                        progress_info = tracker.get_progress_info()
                        await asyncio.get_event_loop().run_in_executor(
                            None,
                            lambda: progress_callback(
                                progress_info["progress"],
                                f"构建层次: {i+1}/{len(sorted_folders)}",
                                folder_path,
                                i+1,
                                len(sorted_folders),
                                progress_info["elapsed_time"]
                            )
                        )

                except Exception as e:
                    self.logger.warning(f"构建文件夹层次失败 {folder_path}: {e}")
                    continue

            self.logger.info("文件夹层次关系构建完成")

        except Exception as e:
            self.logger.error(f"构建文件夹层次失败: {e}")

    async def build_index_async(self, file_list: List[FileInfo], progress_callback: Optional[Callable] = None):
        """
        构建索引（兼容性方法）

        参数:
            file_list: 文件信息列表
            progress_callback: 进度回调函数
        """
        # 调用新的文件夹索引构建方法
        success = await self.build_folder_index_async(file_list, progress_callback)

        if success:
            # 同时更新文件的folder_id字段
            await self._update_files_folder_references(file_list, progress_callback)

        return success

    async def _calculate_folder_hashes(self, folders_map: Dict[str, FolderDTO],
                                      tracker: ProgressTracker,
                                      progress_callback: Optional[Callable] = None) -> None:
        """
        计算文件夹哈希值

        参数:
            folders_map: 文件夹映射
            tracker: 进度跟踪器
            progress_callback: 进度回调函数
        """
        try:
            folder_paths = list(folders_map.keys())

            # 批量计算哈希
            hash_results = await self.hash_manager.batch_calculate_hashes(folder_paths, max_concurrent=3)

            # 更新文件夹DTO的哈希值
            for folder_path, hash_value in hash_results.items():
                if folder_path in folders_map and hash_value:
                    folders_map[folder_path].files_hash = hash_value
                    folders_map[folder_path].last_hash_update = tracker.start_time

            self.logger.info(f"计算了{len(hash_results)}个文件夹的哈希值")

        except Exception as e:
            self.logger.error(f"计算文件夹哈希失败: {e}")

    async def _save_folders_batch(self, folders: List[FolderDTO],
                                tracker: ProgressTracker,
                                progress_callback: Optional[Callable] = None) -> bool:
        """
        批量保存文件夹数据

        参数:
            folders: 文件夹DTO列表
            tracker: 进度跟踪器
            progress_callback: 进度回调函数

        返回:
            是否保存成功
        """
        try:
            # 批量保存
            success_count = await self.folder_repository.batch_create_folders(folders)

            success = success_count == len(folders)
            if success:
                self.logger.info(f"批量保存文件夹成功: {success_count}/{len(folders)}")
            else:
                self.logger.warning(f"部分文件夹保存失败: {success_count}/{len(folders)}")

            return success

        except Exception as e:
            self.logger.error(f"批量保存文件夹失败: {e}")
            return False

    async def _update_files_folder_references(self, file_list: List[FileInfo],
                                            progress_callback: Optional[Callable] = None) -> None:
        """
        更新文件的folder_id引用

        参数:
            file_list: 文件信息列表
            progress_callback: 进度回调函数
        """
        try:
            self.logger.info("开始更新文件的文件夹引用")

            for i, file_info in enumerate(file_list):
                try:
                    # 获取文件所在的文件夹
                    import os
                    folder_path = os.path.dirname(file_info.file_path)

                    if folder_path:
                        # 查找对应的文件夹
                        folder_dto = await self.folder_repository.get_folder_by_path(folder_path)
                        if folder_dto:
                            # 更新文件的folder_id字段
                            file_info.metadata = file_info.metadata or {}
                            file_info.metadata["folder_id"] = folder_dto.folder_id

                    # 更新进度
                    if progress_callback and i % 200 == 0:
                        await asyncio.get_event_loop().run_in_executor(
                            None,
                            lambda: progress_callback(
                                i / len(file_list),
                                f"更新文件引用: {i+1}/{len(file_list)}",
                                file_info.file_path,
                                i+1,
                                len(file_list),
                                0
                            )
                        )

                except Exception as e:
                    self.logger.warning(f"更新文件引用失败 {file_info.file_path}: {e}")
                    continue

            # 批量更新文件信息
            dict_list = [f.to_dict() for f in file_list]
            await asyncio.to_thread(self.db_manager.batch_insert_file_info, dict_list)

            self.logger.info("文件夹引用更新完成")

        except Exception as e:
            self.logger.error(f"更新文件夹引用失败: {e}")

    async def update_index_async(self, changes: List[FileInfo], progress_callback: Optional[Callable] = None):
        """
        更新索引（重构版）

        参数:
            changes: 变更的文件信息列表
            progress_callback: 进度回调函数
        """
        try:
            self.logger.info(f"开始更新索引，变更文件数量: {len(changes)}")

            # 提取受影响的文件夹
            affected_folders = set()
            for file_info in changes:
                import os
                folder_path = os.path.dirname(file_info.file_path)
                if folder_path:
                    affected_folders.add(folder_path)

            # 重新计算受影响文件夹的哈希
            for folder_path in affected_folders:
                try:
                    new_hash = await self.hash_manager.calculate_folder_hash(folder_path, force_recalculate=True)
                    if new_hash:
                        folder_dto = await self.folder_repository.get_folder_by_path(folder_path)
                        if folder_dto:
                            await self.folder_repository.update_folder_hash(folder_dto.folder_id, new_hash)
                except Exception as e:
                    self.logger.warning(f"更新文件夹哈希失败 {folder_path}: {e}")

            # 更新文件信息
            dict_list = [f.to_dict() for f in changes]
            await asyncio.to_thread(self.db_manager.batch_insert_file_info, dict_list)

            # 清空相关缓存
            self._clear_affected_cache(affected_folders)

            self.logger.info(f"索引更新完成，影响了{len(affected_folders)}个文件夹")

        except Exception as e:
            self.logger.error(f"更新索引失败: {e}")

    def _clear_cache(self):
        """清空所有缓存"""
        self._folder_cache.clear()
        self._file_cache.clear()
        self._children_cache.clear()
        self.logger.debug("已清空所有缓存")

    def _clear_affected_cache(self, affected_folders: set):
        """清空受影响的缓存"""
        for folder_path in affected_folders:
            # 清空相关的缓存项
            keys_to_remove = []
            for key in self._children_cache.keys():
                if folder_path in key:
                    keys_to_remove.append(key)

            for key in keys_to_remove:
                self._children_cache.pop(key, None)

        self.logger.debug(f"已清空{len(affected_folders)}个文件夹的相关缓存")

    def clear_cache(self):
        """清空缓存（兼容性方法）"""
        self._clear_cache()

    def get_cache_stats(self):
        """获取缓存统计信息"""
        return {
            "folder_cache_size": len(self._folder_cache),
            "file_cache_size": len(self._file_cache),
            "children_cache_size": len(self._children_cache),
            "cache_hits": self.cache_hits,
            "cache_misses": self.cache_misses,
            "hit_rate": self.cache_hits / (self.cache_hits + self.cache_misses) if (self.cache_hits + self.cache_misses) > 0 else 0
        }