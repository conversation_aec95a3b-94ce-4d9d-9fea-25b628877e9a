#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库适配器基类

定义了数据库适配器的通用接口，所有具体的数据库适配器都需要实现这个接口。

作者: AI助手
日期: 2024-01-01
版本: 1.0.0
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Tuple, Iterator
from datetime import datetime

from ..utils.exceptions import DatabaseConnectionError, CleanupError, UnsupportedOperationError


class DatabaseAdapter(ABC):
    """数据库适配器基类"""
    
    def __init__(self, connection_params: Dict[str, Any]):
        """
        初始化数据库适配器
        
        参数:
            connection_params: 数据库连接参数
        """
        self.connection_params = connection_params
        self.connection = None
        self.is_connected = False
        self._metadata_cache = {}
    
    @abstractmethod
    async def connect(self) -> bool:
        """
        连接到数据库
        
        返回:
            连接是否成功
        """
        pass
    
    @abstractmethod
    async def disconnect(self) -> None:
        """断开数据库连接"""
        pass
    
    @abstractmethod
    async def test_connection(self) -> bool:
        """
        测试数据库连接
        
        返回:
            连接是否正常
        """
        pass
    
    @abstractmethod
    async def get_database_info(self) -> Dict[str, Any]:
        """
        获取数据库信息
        
        返回:
            数据库信息字典
        """
        pass
    
    @abstractmethod
    async def list_collections(self) -> List[str]:
        """
        列出所有集合/表
        
        返回:
            集合/表名称列表
        """
        pass
    
    @abstractmethod
    async def get_collection_info(self, collection_name: str) -> Dict[str, Any]:
        """
        获取集合/表信息
        
        参数:
            collection_name: 集合/表名称
        
        返回:
            集合/表信息字典
        """
        pass
    
    @abstractmethod
    async def count_documents(self, collection_name: str, 
                            conditions: Dict[str, Any] = None) -> int:
        """
        统计文档/记录数量
        
        参数:
            collection_name: 集合/表名称
            conditions: 查询条件
        
        返回:
            文档/记录数量
        """
        pass
    
    @abstractmethod
    async def delete_documents(self, collection_name: str, 
                             conditions: Dict[str, Any] = None,
                             batch_size: int = 1000) -> int:
        """
        删除文档/记录
        
        参数:
            collection_name: 集合/表名称
            conditions: 删除条件
            batch_size: 批处理大小
        
        返回:
            删除的文档/记录数量
        """
        pass
    
    @abstractmethod
    async def truncate_collection(self, collection_name: str) -> bool:
        """
        清空集合/表
        
        参数:
            collection_name: 集合/表名称
        
        返回:
            操作是否成功
        """
        pass
    
    @abstractmethod
    async def backup_collection(self, collection_name: str, 
                               backup_path: str,
                               conditions: Dict[str, Any] = None,
                               format: str = "json") -> str:
        """
        备份集合/表数据
        
        参数:
            collection_name: 集合/表名称
            backup_path: 备份文件路径
            conditions: 备份条件
            format: 备份格式
        
        返回:
            备份文件路径
        """
        pass
    
    @abstractmethod
    async def restore_collection(self, collection_name: str, 
                               backup_path: str,
                               format: str = "json") -> bool:
        """
        恢复集合/表数据
        
        参数:
            collection_name: 集合/表名称
            backup_path: 备份文件路径
            format: 备份格式
        
        返回:
            恢复是否成功
        """
        pass
    
    async def get_sample_documents(self, collection_name: str, 
                                 limit: int = 5) -> List[Dict[str, Any]]:
        """
        获取样本文档/记录
        
        参数:
            collection_name: 集合/表名称
            limit: 样本数量
        
        返回:
            样本文档/记录列表
        """
        # 默认实现，子类可以重写
        raise UnsupportedOperationError(
            f"获取样本文档功能未实现", 
            operation="get_sample_documents",
            database_type=self.__class__.__name__
        )
    
    async def get_indexes(self, collection_name: str) -> List[Dict[str, Any]]:
        """
        获取集合/表的索引信息
        
        参数:
            collection_name: 集合/表名称
        
        返回:
            索引信息列表
        """
        # 默认实现，子类可以重写
        raise UnsupportedOperationError(
            f"获取索引信息功能未实现",
            operation="get_indexes", 
            database_type=self.__class__.__name__
        )
    
    async def analyze_collection(self, collection_name: str) -> Dict[str, Any]:
        """
        分析集合/表
        
        参数:
            collection_name: 集合/表名称
        
        返回:
            分析结果
        """
        try:
            info = await self.get_collection_info(collection_name)
            count = await self.count_documents(collection_name)
            
            analysis = {
                "name": collection_name,
                "document_count": count,
                "info": info,
                "analyzed_at": datetime.now().isoformat()
            }
            
            # 尝试获取样本数据
            try:
                samples = await self.get_sample_documents(collection_name, 3)
                analysis["sample_documents"] = samples
            except UnsupportedOperationError:
                pass
            
            # 尝试获取索引信息
            try:
                indexes = await self.get_indexes(collection_name)
                analysis["indexes"] = indexes
            except UnsupportedOperationError:
                pass
            
            return analysis
            
        except Exception as e:
            raise CleanupError(
                f"分析集合失败: {e}",
                operation="analyze_collection",
                target=collection_name
            )
    
    async def discover_schema(self) -> Dict[str, Any]:
        """
        自动发现数据库结构
        
        返回:
            数据库结构信息
        """
        try:
            db_info = await self.get_database_info()
            collections = await self.list_collections()
            
            schema = {
                "database_info": db_info,
                "collections": {},
                "discovered_at": datetime.now().isoformat()
            }
            
            # 分析每个集合
            for collection in collections:
                try:
                    analysis = await self.analyze_collection(collection)
                    schema["collections"][collection] = analysis
                except Exception as e:
                    schema["collections"][collection] = {
                        "error": str(e),
                        "analyzed_at": datetime.now().isoformat()
                    }
            
            return schema
            
        except Exception as e:
            raise CleanupError(f"发现数据库结构失败: {e}")
    
    def _validate_connection(self):
        """验证数据库连接"""
        if not self.is_connected:
            raise DatabaseConnectionError("数据库未连接")
    
    def _cache_metadata(self, key: str, value: Any, ttl: int = 300):
        """缓存元数据"""
        self._metadata_cache[key] = {
            "value": value,
            "cached_at": datetime.now(),
            "ttl": ttl
        }
    
    def _get_cached_metadata(self, key: str) -> Optional[Any]:
        """获取缓存的元数据"""
        if key in self._metadata_cache:
            cached = self._metadata_cache[key]
            age = (datetime.now() - cached["cached_at"]).total_seconds()
            
            if age < cached["ttl"]:
                return cached["value"]
            else:
                # 过期，删除缓存
                del self._metadata_cache[key]
        
        return None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.disconnect()
